# Multi-stage build for mini-app (Vite + SolidJS)
FROM oven/bun:1 AS base
WORKDIR /app

# Install turbo globally for better caching
RUN bun add -g turbo

# Copy workspace configuration files
COPY package.json bun.lock turbo.json tsconfig.json ./
COPY packages/typescript-config/package.json ./packages/typescript-config/
COPY packages/eslint-config/package.json ./packages/eslint-config/
COPY packages/db/package.json ./packages/db/
COPY apps/mini-app/package.json ./apps/mini-app/

# Install dependencies using B<PERSON>'s workspace support
RUN bun install --frozen-lockfile

# Copy source code
COPY packages/ ./packages/
COPY apps/mini-app/ ./apps/mini-app/

# Build dependencies and mini-app using Turborepo
ENV NODE_ENV=production
RUN turbo build --filter=@repo/mini-app

# Production stage - serve static files with a simple HTTP server
FROM nginx:alpine AS production
WORKDIR /app

# Copy built static files
COPY --from=base /app/apps/mini-app/dist /usr/share/nginx/html

# Copy custom nginx configuration for SPA
COPY <<EOF /etc/nginx/conf.d/default.conf
server {
    listen 3001;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    # Handle SPA routing
    location / {
        try_files \$uri \$uri/ /index.html;
    }

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self'; connect-src 'self'; frame-ancestors 'self';" always;
    
    # Logging
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
}
EOF

EXPOSE 3001

CMD ["nginx", "-g", "daemon off;"]
