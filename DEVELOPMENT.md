# Monorepo Development Guide

This document outlines the available `bun run` scripts for managing and developing this monorepo. All commands should typically be run from the root of the monorepo.

## Table of Contents

- [Core Monorepo Scripts](#core-monorepo-scripts)
- [Application Specific Scripts](#application-specific-scripts)
  - [Main API](#main-api)
  - [Dashboard](#dashboard)
  - [Mini App](#mini-app)
- [Linting, Formatting & Type Checking](#linting-formatting--type-checking)
- [Database (DB) Management](#database-db-management)
  - [Building the DB Package](#building-the-db-package)
  - [Schema Migrations](#schema-migrations)
  - [Seeding Data](#seeding-data)
  - [Resetting the Database](#resetting-the-database)
  - [Drizzle Kit CLI (Advanced)](#drizzle-kit-cli-advanced)
  - [Checking Exports](#checking-exports)
- [Biome (Linter & Formatter)](#biome-linter--formatter)

## Core Monorepo Scripts

These scripts operate across multiple packages or the entire monorepo.

-   **`bun run build`**: Builds all packages and applications in the monorepo.
    ```bash
    bun run build
    ```
-   **`bun run dev`**: Starts all applications in development mode with hot-reloading.
    ```bash
    bun run dev
    ```
-   **`bun run start`** or **`bun run prod`**: Starts all applications in production mode (assumes they have been built).
    ```bash
    bun run start
    # or
    bun run prod
    ```
-   **`bun run lint`**: Runs linters across all relevant packages.
    ```bash
    bun run lint
    ```
-   **`bun run typecheck`**: Performs static type checking across all relevant packages.
    ```bash
    bun run typecheck
    ```
-   **`bun run clean`**: Removes build artifacts (`dist/`, `.turbo/`, `tsconfig.tsbuildinfo`, etc.) from all packages.
    ```bash
    bun run clean
    ```
-   **`bun run format`**: Formats code across all relevant packages (likely using Biome, see below).
    ```bash
    bun run format
    ```

## Application Specific Scripts

These scripts target individual applications within the monorepo.

### Main API

-   **`bun run start:main-api`** or **`bun run prod:main-api`**: Starts the Main API in production mode.
    ```bash
    bun run start:main-api
    ```
-   **`bun run dev:main-api`**: Starts the Main API in development mode.
    ```bash
    bun run dev:main-api
    ```

### Dashboard

-   **`bun run start:dashboard`** or **`bun run prod:dashboard`**: Starts the Dashboard application in production mode.
    ```bash
    bun run start:dashboard
    ```
-   **`bun run dev:dashboard`**: Starts the Dashboard application in development mode.
    ```bash
    bun run dev:dashboard
    ```

### Mini App

-   **`bun run start:mini-app`** or **`bun run prod:mini-app`**: Starts the Mini App in production mode.
    ```bash
    bun run start:mini-app
    ```
-   **`bun run dev:mini-app`**: Starts the Mini App in development mode.
    ```bash
    bun run dev:mini-app
    ```

## Database (DB) Management

Scripts for managing the `@monorepo/db` package, which handles database schema, migrations, and seeding using Drizzle ORM.

### Building the DB Package

-   **`bun run db:build`**: Compiles the TypeScript schema and other source files in the `@monorepo/db` package to JavaScript in its `dist/` directory. This is often a prerequisite for other DB commands that consume the compiled schema.
    ```bash
    bun run db:build
    ```

### Schema Migrations

Migrations allow you to version control your database schema and apply changes incrementally.

-   **`bun run db:migrations:generate`**:
    1.  Ensures the `@repo/db` package is built.
    2.  Analyzes changes in your schema files (`packages/db/src/schema/` and `packages/db/src/enums/`).
    3.  Generates new SQL migration files in `packages/db/drizzle/`.
    4.  Prompts you interactively if you want to apply the newly generated migrations immediately.
    ```bash
    bun run db:migrations:generate
    ```
    **Note:** If you modify schema files, run this command to create a new migration.

-   **`bun run db:migrations:apply`**: Applies any pending (not yet applied) migration files from `packages/db/drizzle/` to your database. This updates your database schema to the latest version defined by the migrations.
    ```bash
    bun run db:migrations:apply
    ```

### Seeding Data

-   **`bun run db:seed`**: Populates the database with initial data (e.g., default tenants, roles, users). This script is typically run after the database schema has been created/updated by migrations.
    ```bash
    bun run db:seed
    ```

### Resetting the Database

These scripts are useful for development and testing to get a clean database state.
**Caution:** These commands will result in data loss in the target database.

-   **`bun run db:reset:drop-recreate`**: Drops the existing database (as defined in your `DATABASE_URL`) and creates a new, empty one with the same name.
    ```bash
    bun run db:reset:drop-recreate
    ```
-   **`bun run db:reset:full`**: Performs a complete database reset by sequentially:
    1.  Dropping and recreating the database (`db:reset:drop-recreate`).
    2.  Applying all migrations to set up the schema (`db:migrations:apply`).
    3.  Seeding initial data (`db:seed`).
    ```bash
    bun run db:reset:full
    ```

### Drizzle Kit CLI (Advanced)

These scripts provide direct access to Drizzle Kit commands. They usually require the `@monorepo/db` package to be built first, as they rely on `drizzle.config.ts` which points to the compiled schema.

-   **`bun run db:kit:generate`**: Directly invokes `drizzle-kit generate`. Useful if you want to generate migrations without the interactive apply step from `db:migrations:generate`. (Requires `db:build` first).
    ```bash
    bun run db:build && bun run db:kit:generate
    ```
-   **`bun run db:kit:push`**: **(Use with caution, especially in team environments or production)**. Attempts to "push" your current schema (from `dist/`) directly to the database, making necessary changes without creating explicit migration files. Good for rapid prototyping but less safe for controlled schema evolution. (Requires `db:build` first).
    ```bash
    bun run db:build && bun run db:kit:push
    ```
-   **`bun run db:kit:studio`**: Starts Drizzle Studio, a web-based GUI to explore and manage your database schema and data. (Requires `db:build` first).
    ```bash
    bun run db:build && bun run db:kit:studio
    ```

### Checking Exports

-   **`bun run db:check:exports`**: Verifies that all `.ts` files within `packages/db/src/schema/` and `packages/db/src/enums/` (excluding `index.ts` files themselves) are correctly exported from their respective `index.ts` files (e.g., `export * from "./some-schema.js";`). This helps catch errors if new schema/enum files are added but not exposed through the main index files.
    ```bash
    bun run db:check:exports
    ```

## Biome (Linter & Formatter)

[Biome](https://biomejs.dev/) is used for linting and formatting the codebase.

-   **`bun run format:check`**: Checks for formatting issues across the entire monorepo without applying changes.
    ```bash
    bun run format:check
    ```
-   **`bun run format:fix`**: Applies Biome's auto-formatting to fix issues across the entire monorepo.
    ```bash
    bun run format:fix
    ```
-   **`bun run lint:biome`**: Runs Biome's linter to find code quality issues.
    ```bash
    bun run lint:biome
    ```
-   **`bun run lint:biome:fix`**: Runs Biome's linter and attempts to auto-fix any fixable issues.
    ```bash
    bun run lint:biome:fix
    ```
-   **`bun run format:biome`**: An alias, likely similar to `format:check` or intended to just format (same as `format:fix` if it writes).
    ```bash
    bun run format:biome
    ```
-   **`bun run format:biome:fix`**: An alias, likely the same as `format:fix`.
    ```bash
    bun run format:biome:fix
    ```

---
