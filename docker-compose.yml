# Docker Compose configuration for Taxi29 monorepo
services:
  main-api:
    container_name: taxi29-main-api
    restart: unless-stopped
    ports:
      - "3004:3004"
    build:
      context: .
      dockerfile: Dockerfile.main-api-turbo
      target: production
    environment:
      - NODE_ENV=production
      - PORT=3004
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_HOST=redis
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - PRIMARY_BOT_TOKEN=${PRIMARY_BOT_TOKEN}
      - TOKEN_ENCRYPTION_KEY=${TOKEN_ENCRYPTION_KEY}
    depends_on:
      db-migrator:
          condition: service_completed_successfully
      redis:
          condition: service_started
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3004/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - taxi29-network


  mini-app:
    container_name: taxi29-mini-app
    restart: unless-stopped
    ports:
      - "3001:3001"
    build:
      context: .
      dockerfile: Dockerfile.mini-app
      target: production
    environment:
      - NODE_ENV=production
    depends_on:
      - main-api
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3001/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - taxi29-network

  dashboard:
    container_name: taxi29-dashboard
    restart: unless-stopped
    ports:
      - "3000:3000"
    build:
      context: .
      dockerfile: Dockerfile.dashboard
      target: runner
    environment:
      - NODE_ENV=production
      - PORT=3000
      - HOSTNAME=0.0.0.0
      - PROD_API_URL=http://main-api:3004
    depends_on:
      - main-api
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - taxi29-network

  postgres:
    container_name: taxi29-postgres
    image: postgis/postgis:latest
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=${POSTGRES_USER:-taxi29}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB:-taxi29}
    volumes:
      - ./packages/docker-config/init-db.sh:/docker-entrypoint-initdb.d/init-db.sh
      - pg_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-taxi29} -d ${POSTGRES_DB:-taxi29}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - taxi29-network

  db-migrator:
    container_name: taxi29-db-migrator
    build:
      context: .
      dockerfile: Dockerfile.migrator
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - DB_HOST=postgres
      - DB_PORT=5432
    depends_on:
      postgres:
        condition: service_healthy
    command: ["bun", "run", "packages/db/scripts/migrate.ts"]
    networks:
      - taxi29-network

  redis:
    container_name: taxi29-redis
    image: redis:latest
    command: ["redis-server", "--maxmemory-policy", "noeviction"]
    restart: unless-stopped
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - taxi29-network

  rabbitmq:
    container_name: taxi29-rabbitmq
    image: rabbitmq:3-management
    restart: unless-stopped
    ports:
      - "${RABBITMQ_PORT:-5672}:5672"
      - "15672:15672"
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_USER:-taxi29}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_PASSWORD}
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "check_running"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - taxi29-network

networks:
  taxi29-network:
    driver: bridge

volumes:
  redis_data:
  rabbitmq_data:
  pg_data:
