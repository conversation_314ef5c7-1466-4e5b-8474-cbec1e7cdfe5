# packages/docker-config/docker-compose.yml
services:
  main-api:
    container_name: gramio-bot
    restart: unless-stopped
    ports:
      - "3004:3004"
    build:
      context: .
      dockerfile: Dockerfile.main-api-turbo
    environment:
      - NODE_ENV=production
      - PORT=3004
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_HOST=redis
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - PRIMARY_BOT_TOKEN=${PRIMARY_BOT_TOKEN}
      - TOKEN_ENCRYPTION_KEY=${TOKEN_ENCRYPTION_KEY}
    depends_on:
      db-migrator: 
          condition: service_completed_successfully 
      redis:
          condition: service_started
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3004/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s


  mini-app:
    container_name: mini-app-gramio-bot
    restart: unless-stopped
    ports:
      - 3001:3001
    build:
      context: .
      dockerfile: Dockerfile.mini-app
    environment:
      - NODE_ENV=production
    depends_on:
      - main-api

  dashboard:
    container_name: dashboard
    restart: unless-stopped
    ports:
      - 3000:3000
    build:
      context: .
      dockerfile: Dockerfile.dashboard
    environment:
      - NODE_ENV=production
      - PORT=3000
      - HOSTNAME=0.0.0.0
      - PROD_API_URL=http://main-api:3004
    depends_on:
      - main-api

  postgres:
    container_name: postgres-postgis
    image: postgis/postgis:latest
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=${POSTGRES_USER:-gramio}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_USER:-gramio}
    volumes:
      - ./packages/docker-config/init-db.sh:/docker-entrypoint-initdb.d/init-db.sh
      - pg_data:/var/lib/postgresql/data
    healthcheck: 
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-gramio} -d ${POSTGRES_DB:-gramio}"]
      interval: 10s
      timeout: 5s
      retries: 5

  db-migrator:
    container_name: gramio-db-migrator
    build:
      context: . 
      dockerfile: Dockerfile.migrator
    environment:
      - DATABASE_URL=${DATABASE_URL} # Crucial: pass the DB URL
      # Add any other env vars needed by migrate.ts (though usually just DATABASE_URL)
      - DB_HOST=postgres # For the entrypoint wait script
      - DB_PORT=5432
    depends_on:
      postgres:
        condition: service_healthy # Wait for postgres to be healthy
    # Command to actually run the migrations. Adjust path/script as necessary.
    command: ["bun", "run", "packages/db/scripts/migrate.ts"]
    # This service runs to completion and then exits.
    # No restart policy needed unless you want it to retry on failure.

  redis:
    container_name: gramio-redis
    image: redis:latest
    command: ["redis-server", "--maxmemory-policy", "noeviction"]
    restart: unless-stopped
    volumes:
      - redis_data:/data
    ports:
      - 6379:6379


  rabbitmq:
      image: rabbitmq:3-management
      container_name: rabbitmq1
      ports:
          - "${RABBITMQ_PORT}:5672"
          - "15672:15672"
      environment:
          RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER}
          RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD}
      volumes:
          - rabbitmq_data:/var/lib/rabbitmq
      healthcheck:
          test: [ "CMD", "rabbitmq-diagnostics", "check_running" ]
          interval: 30s
          timeout: 10s
          retries: 3
          start_period: 30s


volumes:
    redis_data:
    rabbitmq_data:
    pg_data:
