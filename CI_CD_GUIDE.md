# CI/CD Pipeline Guide

This document describes the comprehensive CI/CD pipeline setup for the Taxi29 monorepo.

## 🏗️ Pipeline Overview

Our CI/CD pipeline consists of four main workflows:

1. **CI Pipeline** (`ci.yml`) - Continuous Integration
2. **CD Pipeline** (`cd.yml`) - Continuous Deployment  
3. **Release Pipeline** (`release.yml`) - Release Management
4. **Maintenance Pipeline** (`maintenance.yml`) - Automated Maintenance

## 🔄 CI Pipeline (Continuous Integration)

**Triggers:**
- Push to `main` or `develop` branches
- Pull requests to `main` or `develop` branches

**Jobs:**
1. **Code Quality** - Linting, type checking, formatting
2. **Build and Test** - Matrix build for all packages
3. **Docker Build** - Build and test Docker images
4. **Integration Tests** - End-to-end testing with real services
5. **Security Scanning** - Vulnerability scanning with Trivy
6. **Performance Tests** - Performance benchmarks (main branch only)

**Key Features:**
- ✅ Parallel execution for faster feedback
- ✅ Matrix strategy for testing all packages
- ✅ Artifact caching for improved performance
- ✅ Security scanning integration
- ✅ Comprehensive test coverage

## 🚀 CD Pipeline (Continuous Deployment)

**Triggers:**
- Push to `main` branch (staging deployment)
- Git tags starting with `v*` (production deployment)
- Manual workflow dispatch

**Jobs:**
1. **Build and Push** - Build and push Docker images to registry
2. **Deploy to Staging** - Automatic staging deployment
3. **Deploy to Production** - Production deployment (tags only)
4. **Database Migration** - Production database updates
5. **Rollback** - Manual rollback capability
6. **Cleanup** - Remove old container images
7. **Monitoring** - Setup alerts and notifications

**Key Features:**
- ✅ Multi-platform Docker builds (AMD64, ARM64)
- ✅ Environment-specific deployments
- ✅ Database migration handling
- ✅ Rollback capabilities
- ✅ Automated cleanup

## 📦 Release Pipeline

**Triggers:**
- Git tags starting with `v*`
- Manual workflow dispatch with version input

**Jobs:**
1. **Validate Release** - Version format validation
2. **Full Test Suite** - Comprehensive testing
3. **Generate Release Notes** - Automatic changelog generation
4. **Build Assets** - Create release artifacts
5. **Create GitHub Release** - Publish release with assets
6. **Update Documentation** - Update README and CHANGELOG
7. **Notify Release** - Send notifications

**Key Features:**
- ✅ Semantic version validation
- ✅ Automatic release notes generation
- ✅ Release asset creation
- ✅ Documentation updates
- ✅ Pre-release support

## 🔧 Maintenance Pipeline

**Triggers:**
- Daily schedule (2 AM UTC)
- Manual workflow dispatch

**Jobs:**
1. **Dependency Updates** - Check for outdated packages
2. **Security Audit** - Regular security scanning
3. **Code Quality Metrics** - Generate quality reports
4. **Performance Check** - Monitor build performance
5. **Docker Cleanup** - Remove old images
6. **Health Check** - Verify production services
7. **Backup Verification** - Ensure backups are working

**Key Features:**
- ✅ Automated dependency monitoring
- ✅ Regular security audits
- ✅ Performance tracking
- ✅ Production health monitoring
- ✅ Backup verification

## 🛠️ Setup Instructions

### 1. Repository Secrets

Configure the following secrets in your GitHub repository:

```bash
# Container Registry
GITHUB_TOKEN                 # Automatically provided by GitHub

# Production Environment
PROD_DATABASE_URL           # Production database connection
PROD_REDIS_URL              # Production Redis connection
PROD_BOT_TOKEN              # Production Telegram bot token
PROD_ENCRYPTION_KEY         # Production encryption key

# Staging Environment  
STAGING_DATABASE_URL        # Staging database connection
STAGING_REDIS_URL           # Staging Redis connection
STAGING_BOT_TOKEN           # Staging Telegram bot token
STAGING_ENCRYPTION_KEY      # Staging encryption key

# Notifications (Optional)
SLACK_WEBHOOK_URL           # Slack notifications
DISCORD_WEBHOOK_URL         # Discord notifications
```

### 2. Environment Configuration

Create environment-specific configurations:

**Staging Environment:**
- URL: `https://staging.taxi29.example.com`
- API: `https://staging-api.taxi29.example.com`
- Database: Separate staging database
- Redis: Separate staging Redis instance

**Production Environment:**
- URL: `https://taxi29.example.com`
- API: `https://api.taxi29.example.com`
- Database: Production database with backups
- Redis: Production Redis with persistence

### 3. Branch Protection Rules

Configure branch protection for `main` branch:

```yaml
Required status checks:
  - Code Quality
  - Build and Test (main-api)
  - Build and Test (dashboard)
  - Build and Test (mini-app)
  - Build and Test (db)
  - Docker Build
  - Integration Tests

Require branches to be up to date: ✅
Require pull request reviews: ✅ (1 reviewer)
Dismiss stale reviews: ✅
Restrict pushes to matching branches: ✅
```

## 📊 Monitoring and Alerts

### Build Status Badges

Add these badges to your README:

```markdown
![CI](https://github.com/your-org/taxi29/workflows/CI%20Pipeline/badge.svg)
![CD](https://github.com/your-org/taxi29/workflows/CD%20Pipeline/badge.svg)
![Release](https://github.com/your-org/taxi29/workflows/Release/badge.svg)
```

### Notifications

The pipeline can send notifications to:
- GitHub Issues (for failures and maintenance)
- Slack/Discord (for deployments)
- Email (for critical alerts)

## 🚨 Troubleshooting

### Common Issues

1. **Build Failures**
   - Check dependency compatibility
   - Verify TypeScript configuration
   - Review test failures

2. **Docker Build Issues**
   - Ensure Dockerfiles are updated
   - Check for missing dependencies
   - Verify workspace references

3. **Deployment Failures**
   - Check environment secrets
   - Verify database connectivity
   - Review service health checks

4. **Test Failures**
   - Check test database setup
   - Verify environment variables
   - Review integration test configuration

### Debug Commands

```bash
# Local testing
bun run build                    # Test build locally
bun run test                     # Run tests locally
docker-compose up --build       # Test Docker setup

# CI debugging
gh workflow run ci.yml           # Trigger CI manually
gh workflow run cd.yml           # Trigger CD manually
gh run list                      # List recent runs
gh run view <run-id>             # View specific run
```

## 📈 Performance Optimization

### Build Performance
- ✅ Turborepo caching enabled
- ✅ Docker layer caching
- ✅ Parallel job execution
- ✅ Artifact reuse between jobs

### Resource Usage
- ✅ Matrix builds for parallel execution
- ✅ Conditional job execution
- ✅ Efficient Docker multi-stage builds
- ✅ Cleanup of old artifacts

## 🔐 Security Best Practices

- ✅ Secrets management with GitHub Secrets
- ✅ Regular vulnerability scanning
- ✅ Container image scanning
- ✅ Dependency audit automation
- ✅ Least privilege access
- ✅ Environment isolation

## 📚 Additional Resources

- [GitHub Actions Documentation](https://docs.github.com/en/actions)
- [Turborepo CI/CD Guide](https://turborepo.com/docs/ci)
- [Docker Best Practices](https://docs.docker.com/develop/dev-best-practices/)
- [Bun CI/CD Guide](https://bun.sh/docs/test/ci)

---

**Note**: This CI/CD setup is designed to be production-ready and follows industry best practices for monorepo management, security, and deployment automation.
