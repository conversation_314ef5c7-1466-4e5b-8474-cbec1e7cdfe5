# Taxi29 Monorepo

A modern monorepo for the Taxi29 Telegram bot ecosystem, built with Turborepo, Bun, and TypeScript.

## 🏗️ Architecture

This monorepo contains three main applications and shared packages:

### Applications
- **`@repo/main-api`** - NestJS Telegram bot API service (Port 3004)
- **`@repo/dashboard`** - Next.js admin dashboard (Port 3000)
- **`@repo/mini-app`** - SolidJS Telegram mini app (Port 3001)

### Packages
- **`@repo/db`** - Database schema and utilities using Drizzle ORM
- **`@repo/typescript-config`** - Shared TypeScript configurations
- **`@repo/eslint-config`** - Shared ESLint configurations

## 🚀 Quick Start

### Prerequisites
- [Bun](https://bun.sh) >= 1.2.3
- [Docker](https://docker.com) and Docker Compose
- [Node.js](https://nodejs.org) >= 18 (for some tools)

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd taxi29

# Install dependencies
bun install

# Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# Build all packages
bun run build

# Start development servers
bun run dev
```

## 📦 Package Management

This monorepo uses Bun workspaces for dependency management. All packages use the `@repo/` namespace for consistency.

### Adding Dependencies

```bash
# Add to root (affects all packages)
bun add <package>

# Add to specific package
bun add <package> --filter @repo/main-api

# Add dev dependency
bun add -D <package> --filter @repo/dashboard
```

### Workspace Dependencies

Internal packages reference each other using `workspace:*`:

```json
{
  "dependencies": {
    "@repo/db": "workspace:*"
  }
}
```

## 🔧 Development

### Available Scripts

```bash
# Development
bun run dev                    # Start all apps in development mode
bun run dev:main-api          # Start only main-api
bun run dev:dashboard         # Start only dashboard
bun run dev:mini-app          # Start only mini-app

# Building
bun run build                 # Build all packages and apps
bun run build:main-api        # Build only main-api
bun run build:dashboard       # Build only dashboard
bun run build:mini-app        # Build only mini-app

# Testing
bun run test                  # Run all tests
bun run lint                  # Lint all packages
bun run typecheck             # Type check all packages

# Database
bun run db:build              # Build database package
bun run db:migrations:generate # Generate new migrations
bun run db:migrations:apply   # Apply pending migrations
bun run db:seed               # Seed database with test data
bun run db:kit:studio         # Open Drizzle Studio
```

### Turborepo Pipeline

The build system uses Turborepo for optimal caching and task orchestration:

- **Dependencies**: `@repo/db` must build before apps that depend on it
- **Caching**: Build outputs are cached for faster subsequent builds
- **Parallelization**: Independent tasks run in parallel

## 🐳 Docker Deployment

### Development

```bash
# Start all services
docker-compose up

# Start specific service
docker-compose up main-api

# Build and start
docker-compose up --build
```

### Services

- **main-api**: `http://localhost:3004`
- **dashboard**: `http://localhost:3000`
- **mini-app**: `http://localhost:3001`
- **postgres**: `localhost:5432`
- **redis**: `localhost:6379`
- **rabbitmq**: `localhost:15672` (management UI)

## 📚 Documentation

- [Development Guide](./DEVELOPMENT.md)
- [Docker Deployment](./DOCKER_DEPLOYMENT.md)
- [Database Schema](./docs/DB/README.md)

## 📄 License

This project is licensed under the MIT License.
