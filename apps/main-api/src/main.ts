import "reflect-metadata";
import { NestFactory } from "@nestjs/core";
import { AppModule } from "./app.module.js"; // <<<< .js extension
import { AppConfigService } from "./config/app-config.service.js"; // <<<< .js extension

async function bootstrap() {
	try {
		const app = await NestFactory.create(AppModule);
		const configService = app.get(AppConfigService);
		const port = configService.appPort || process.env.PORT || 3004; // Use a different port if 3000 is taken
		await app.listen(port);
		console.log(`Application is running on: http://localhost:${port}`);
	} catch (error) {
		console.error("FATAL: Failed to bootstrap the NestJS application:", error);
		process.exit(1); // Exit with error code
	}
}
bootstrap();
