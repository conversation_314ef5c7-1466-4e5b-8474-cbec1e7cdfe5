import { AppConfigService } from "@/config/app-config.service.js";
import { ConfigModule } from "@/config/config.module.js";
import { Global, Module } from "@nestjs/common";
import { Logger } from "@nestjs/common";
import { ClientsModule, Transport } from "@nestjs/microservices";
import { RedisService } from "./redis.service.js";

export const REDIS_MICROSERVICE_CLIENT = "REDIS_MICROSERVICE_CLIENT";

@Global()
@Module({
	imports: [
		ConfigModule,
		ClientsModule.registerAsync([
			{
				name: REDIS_MICROSERVICE_CLIENT,
				inject: [AppConfigService],
				useFactory: (config: AppConfigService) => {
					const logger = new Logger("RedisModule");
					const options = {
						host: config.redisHost,
						port: config.redisPort,
						...(config.redisPassword ? { password: config.redisPassword } : {}),
						db: config.redisDb,
						keyPrefix: config.redisPrefix,
						retryAttempts: 5,
						retryDelay: 3000,
					};
					logger.log(
						`Connecting to Redis at ${options.host}:${options.port} (db: ${options.db}, prefix: "${options.keyPrefix}")`,
					);
					return {
						transport: Transport.REDIS,
						options,
					};
				},
			},
		]),
	],
	providers: [RedisService],
	exports: [RedisService, ClientsModule],
})
export class RedisModule {}
