import { autoAnswerCallbackQuery } from "@gramio/auto-answer-callback-query";
import { autoRetry } from "@gramio/auto-retry";
import { mediaCache } from "@gramio/media-cache";
import { mediaGroup } from "@gramio/media-group";
import { prompt } from "@gramio/prompt";
import { type AnyScene, scenes } from "@gramio/scenes";
import { session } from "@gramio/session";
import { redisStorage } from "@gramio/storage-redis";
// biome-ignore lint/style/useImportType: <explanation>
import {
	Inject,
	Injectable,
	Logger,
	OnModuleInit,
	forwardRef,
} from "@nestjs/common";
// Keep Bot as GramioBotClass for typing the map
// biome-ignore lint/style/useImportType: <explanation>
import { Bot as GramioBotClass, TelegramUpdate } from "gramio";
// biome-ignore lint/style/useImportType: <explanation>
import { AppConfigService } from "../config/app-config.service.js";
// biome-ignore lint/style/useImportType: <explanation>
import { RedisService } from "../redis/redis.service.js";
import { i18n } from "@/shared/locales/index.js";

// Import the correct context types.
// Use 'import type' for types only to avoid runtime issues.
import type {
	AppBotErrorDefinitions,
	AppDeriveShape, // <<<< .js extension
	AppGlobalDeriveExtensions, // <<<< .js extension
	TenantInfo,
} from "../types/bot-context.js";
import type { BotConfigFromDB } from "../types/database.js"; // Using local database types

import { CallbackQueryHandlersService } from "../commands/callback-query-handlers.service.js";
import { ChosenInlineResultHandlersService } from "../commands/chosen-inline-result-handlers.service.js";
// Import handler services using their class type for DI
// Ensure these imports are runtime imports if the class is used for 'new' or injection
import { CommandHandlersService } from "../commands/command-handlers.service.js";
import { InlineQueryHandlersService } from "../commands/inline-query-handlers.service.js";
import { ReactionHandlersService } from "../commands/reaction-handlers.service.js";
import { StartCommandHandlersService } from "../commands/start-handlers.service.js";
import { SceneCommandHandlersService } from "../scenes/scenes-command-handlers.service.js";

import * as crypto from "node:crypto"; // Node.js crypto module
import { greetingScene } from "../scenes/greeting.scene.js"; // Import actual scenes

// Import the database service
import { DatabaseService } from "../services/database.service.js";

/**
 * Service responsible for processing bot updates for multiple tenants
 */
@Injectable()
export class BotProcessingService implements OnModuleInit {
	private readonly logger = new Logger(BotProcessingService.name);

	// Universal processor for routing and applying global middleware/derives
	// This should be typed as the Gramio Bot instance with your augmented context
	public universalGramioProcessor!: GramioBotClass<
		AppBotErrorDefinitions,
		AppDeriveShape
	>; // Use the correct type

	// Map to store active bot configurations (webhookPathSegment -> BotConfig)
	private activeBotConfigs = new Map<string, BotConfigFromDB>();

	// Map to store the ACTUAL API clients for each active bot (bot ID -> GramioBot client)
	// This is CRITICAL for multi-tenancy API calls
	// Use the correct client type from your AppBot definition
	private tenantBotApiClients = new Map<
		string,
		GramioBotClass<AppBotErrorDefinitions, AppDeriveShape>["api"]
	>(); // Use AppBot['api'] for the client type

	constructor(
		private readonly config: AppConfigService,
		private readonly redisService: RedisService,
		// Use forwardRef() for all handler services to handle potential circular dependencies
		@Inject(forwardRef(() => CommandHandlersService))
		private readonly commandHandlers: CommandHandlersService,
		@Inject(forwardRef(() => CallbackQueryHandlersService))
		private readonly callbackQueryHandlers: CallbackQueryHandlersService,
		@Inject(forwardRef(() => StartCommandHandlersService))
		private readonly startCommandHandlers: StartCommandHandlersService,
		@Inject(forwardRef(() => InlineQueryHandlersService))
		private readonly inlineQueryHandlers: InlineQueryHandlersService,
		@Inject(forwardRef(() => ReactionHandlersService))
		private readonly reactionHandlers: ReactionHandlersService,
		@Inject(forwardRef(() => ChosenInlineResultHandlersService))
		private readonly chosenInlineResultHandlers: ChosenInlineResultHandlersService,
		@Inject(forwardRef(() => SceneCommandHandlersService))
		private readonly sceneCommandHandlers: SceneCommandHandlersService,
		// Use forwardRef() for database service as well
		@Inject(forwardRef(() => DatabaseService))
		private readonly databaseService: DatabaseService,
	) {}

	async onModuleInit() {
		// Load bot configs and initialize their specific API clients BEFORE starting the processor
		await this.loadActiveBotConfigsAndInitializeClients();

		// Use a placeholder token for the universal processor.
		// Its sole purpose is to provide the processing pipeline (middleware, derive, handlers).
		// API calls MUST use the client obtained from `tenantBotApiClients` in the derive function.
		const placeholderToken =
			this.config.primaryBotToken || "INVALID_PLACEHOLDER_TOKEN";
		if (placeholderToken === "INVALID_PLACEHOLDER_TOKEN") {
			this.logger.warn(
				"Using a placeholder token for the universal processor. Ensure PRIMARY_BOT_TOKEN is set for webhook secret validation if needed.",
			);
		}

		const storage = redisStorage(this.redisService.client);
		const allScenes: AnyScene[] = [greetingScene]; // Collect all your scenes here

		// Build the universal processor pipeline
		// Assign the result directly to this.universalGramioProcessor
		const universalProcessor = new GramioBotClass<
			AppBotErrorDefinitions,
			AppDeriveShape
		>(placeholderToken) // Explicitly type Bot instance
			.extend(autoAnswerCallbackQuery())
			.extend(mediaGroup())
			.extend(autoRetry())
			.extend(mediaCache())
			.extend(
				session({
					initial: () => ({}), // Initial session state per user
					storage,
					// Define a getSessionKey that is unique PER TENANT/BOT AND USER
					getSessionKey: (ctx: any) => {
						// Access properties from the update object
						const botConfig = ctx.update?._botConfig;
						const botId = botConfig?.id;
						const tenantId = botConfig?.tenantId;

						// Access userId safely using optional chaining and nullish coalescing
						let telegramIdentifier: number | undefined;
						if (ctx.from?.id) {
							telegramIdentifier = ctx.from.id;
						} else if (ctx.chat?.id) {
							telegramIdentifier = ctx.chat.id;
						}

						if (!tenantId || !botId || !telegramIdentifier) {
							// Log a warning if key cannot be generated, return a non-user specific key
							this.logger.warn(
								`[getSessionKey] Cannot generate session key for update ${ctx.updateId || "unknown"}. tenantId: ${!!tenantId}, botId: ${!!botId}, telegramIdentifier: ${!!telegramIdentifier}.`,
							);
							// Fallback key - consider if this is appropriate or if processing should stop
							return `global_fallback:${ctx.updateId || Date.now()}`; // Using update ID or timestamp as a unique fallback
						}
						// Unique key format: tenantId:botId:telegramUserId/ChatId
						return `${tenantId}:${botId}:${telegramIdentifier}`;
					},
				}),
			)
			.extend(scenes(allScenes, { storage })) // Initialize scenes plugin with all scenes
			.extend(prompt())
			// Use the correct context types for the derive function
			// ContextBeforeAppDerive is the base context coming *into* derive
			// AppDeriveExtensions are the properties *you add*
			.derive(async (ctx: any): Promise<Partial<AppGlobalDeriveExtensions>> => {
				// Retrieve the bot config attached to the update in processUpdateForSegment
				const botConfigForThisUpdate = (ctx.update as any)._botConfig as
					| BotConfigFromDB
					| undefined;

				// Initialize derived properties, always include i18n
				const derivedPropsPartial: Partial<AppGlobalDeriveExtensions> = {
					// Build translator based on user's language code. Use ctx.from?.languageCode safely.
					t: i18n.buildT(ctx.from?.languageCode),
				};

				if (!botConfigForThisUpdate) {
					this.logger.warn(
						`[derive] No botConfig in update for derivation. Update ID: ${ctx.updateId}`,
					);
					// Return only 't' and other non-bot-specifics if no bot config is found
					return derivedPropsPartial;
				}

				// --- CRITICAL: Fetch the correct tenant-specific API client ---
				const tenantApiClient = this.tenantBotApiClients.get(
					botConfigForThisUpdate.id,
				);

				if (!tenantApiClient) {
					// This should ideally not happen if loadActiveBotConfigsAndInitializeClients ran correctly
					this.logger.error(
						`[derive] No API client found for bot ID: ${botConfigForThisUpdate.id}. Update ID: ${ctx.updateId}. Attempting background reload.`,
					);
					// Attempt a reload in background, but cannot provide client for *this* update
					this.loadActiveBotConfigsAndInitializeClients().catch((err) =>
						this.logger.error(
							`Background config reload failed: ${err.message}`,
						),
					);
					// Provide basic botInfo but no API client
					derivedPropsPartial.botInfo = {
						id: botConfigForThisUpdate.id,
						botUsername: botConfigForThisUpdate.botUsername || undefined,
					};
					derivedPropsPartial.tenantId = botConfigForThisUpdate.tenantId;
					return derivedPropsPartial; // Return with limited context
				}

				// Fetch tenant details using the DatabaseService
				let tenantRecord: TenantInfo | undefined;
				try {
					const dbTenant = await this.databaseService.getTenantById(
						botConfigForThisUpdate.tenantId,
					);
					if (dbTenant) {
						tenantRecord = {
							id: dbTenant.id,
							name: dbTenant.name,
						};
					}
					// You might want to add more tenant data here if needed in the context
				} catch (error: any) {
					this.logger.error(
						`Failed to fetch tenant info for ID: ${botConfigForThisUpdate.tenantId}`,
						error,
					);
					// tenantRecord remains undefined on error
				}

				// Populate the rest of the derivedPropsPartial
				derivedPropsPartial.tenant = tenantRecord;
				derivedPropsPartial.botInfo = {
					id: botConfigForThisUpdate.id,
					botUsername: botConfigForThisUpdate.botUsername || undefined,
				};
				derivedPropsPartial.tenantId = botConfigForThisUpdate.tenantId;
				derivedPropsPartial.tenantBotApiClient = tenantApiClient;

				// Fetch user roles for ctx.from?.id or ctx.chat?.id and botConfigForThisUpdate.id
				const telegramIdentifier = ctx.from?.id ?? ctx.chat?.id;
				if (telegramIdentifier && typeof telegramIdentifier === "number") {
					// Pass the Telegram identifier (user or chat ID) and bot ID
					derivedPropsPartial.userRoles =
						await this.databaseService.getUserRoles(
							telegramIdentifier,
							botConfigForThisUpdate.id,
						);
				} else {
					// Handle cases where user/chat ID is not available (e.g., some service messages)
					derivedPropsPartial.userRoles = [];
				}

				// Return the object with potentially all fields set
				return derivedPropsPartial;
			})
			// Use any for error context
			.onError((errCtx: any) => {
				// Log the error with available context
				this.logger.error(
					`GramIO Error (Update: ${errCtx.updateId || "unknown"}):`,
					errCtx.error?.message,
					errCtx.error?.stack, // Log stack trace for better debugging
				);

				// Check for existence of properties needed for sending a message
				if (errCtx.chat?.id) {
					try {
						// Try to send a generic error message
						errCtx
							.reply("An unexpected error occurred. Please try again later.")
							.catch((apiErr: any) => {
								this.logger.error(
									"Failed to send error message to user:",
									apiErr,
								);
							});
					} catch (e) {
						this.logger.warn("Cannot send error message: reply method failed");
					}
				} else {
					this.logger.warn("Cannot send error message: chat.id missing");
				}
			});

		// Assign the correctly typed universal processor (the Bot instance)
		// Use double type assertion to handle complex GramIO typing
		this.universalGramioProcessor =
			universalProcessor as unknown as GramioBotClass<
				AppBotErrorDefinitions,
				AppDeriveShape
			>;

		// Initialize ALL handler services with the universalGramioProcessor
		this.logger.log(
			"Initializing handler services with universal processor...",
		);

		// Pass the correctly typed universalGramioProcessor
		this.commandHandlers.initialize(this.universalGramioProcessor);
		this.callbackQueryHandlers.initialize(this.universalGramioProcessor);
		this.startCommandHandlers.initialize(this.universalGramioProcessor);
		this.inlineQueryHandlers.initialize(this.universalGramioProcessor);
		this.reactionHandlers.initialize(this.universalGramioProcessor);
		this.chosenInlineResultHandlers.initialize(this.universalGramioProcessor);
		this.sceneCommandHandlers.initialize(this.universalGramioProcessor);
		this.logger.log("Handler services initialized.");

		// Register handlers ONCE on the universalGramioProcessor
		this.logger.log("Registering handlers on universal processor...");
		this.commandHandlers.registerHandlers();
		this.callbackQueryHandlers.registerHandlers();
		this.startCommandHandlers.registerHandlers();
		this.inlineQueryHandlers.registerHandlers();
		this.reactionHandlers.registerHandlers();
		this.chosenInlineResultHandlers.registerHandlers();
		if (this.sceneCommandHandlers) {
			// Check if injected (redundant with class injection but safe)
			this.sceneCommandHandlers.registerHandlers();
		}
		this.logger.log("All handlers registered.");

		// Start the universal processor (this starts listening for updates via handleUpdate)
		// No polling needed since we are using webhooks processed by the controller
		// await this.universalGramioProcessor.start(); // Do not call start() here if using webhooks via controller
		this.logger.log(
			"Universal GramIO processor configured for webhook processing.",
		);
	}

	/**
	 * Load active bot configurations from the database and initialize API clients
	 */
	async loadActiveBotConfigsAndInitializeClients() {
		this.logger.log(
			"Loading active bot configurations and initializing API clients...",
		);

		// First verify database configuration
		if (!this.config.isDatabaseConfigured) {
			this.logger.error(
				"Database is not properly configured. Please check DATABASE_URL in .env",
			);
			return;
		}

		try {
			// Fetch active bots from the database using the database service
			const botsFromDB = await this.databaseService.getActiveBots();

			if (!botsFromDB || botsFromDB.length === 0) {
				this.logger.warn("No active bots found in database");
				return;
			}

			// Clear existing configs and clients maps
			this.activeBotConfigs.clear();
			this.tenantBotApiClients.clear();

			for (const botData of botsFromDB) {
				// Cast to your specific type for clarity, assuming structure matches
				const botConfig = botData as BotConfigFromDB;

				// IMPORTANT: Decrypt botConfig.token!
				const decryptedToken = this.decryptToken(botConfig.token);

				// Store the bot config by its webhook segment
				this.activeBotConfigs.set(botConfig.webhookPathSegment, {
					...botConfig,
					token: decryptedToken,
				}); // Store decrypted token here temporarily if needed downstream

				// Initialize a GramioBot instance just to get its client
				// We don't need to start() these individual instances or register handlers on them
				try {
					// Use the correct Bot type when creating the instance
					const tenantBotInstance = new GramioBotClass<
						AppBotErrorDefinitions,
						AppDeriveShape
					>(decryptedToken);
					// Optional: await tenantBotInstance.init(); // If you need bot.info etc.

					// Store the API client instance, keyed by bot ID
					// Ensure the client type matches the universal processor's client type
					this.tenantBotApiClients.set(botConfig.id, tenantBotInstance.api); // Use .api for the client!

					this.logger.log(
						`Initialized API client for bot ID: ${botConfig.id} (Segment: ${botConfig.webhookPathSegment})`,
					);
				} catch (initError: any) {
					this.logger.error(
						`Failed to initialize GramioBot instance for bot ID ${botConfig.id}: ${initError.message}`,
					);
					// Decide how to handle bots with invalid tokens - perhaps mark as errored/disabled in DB
				}
			}

			this.logger.log(
				`Loaded ${this.activeBotConfigs.size} active bot configurations and ${this.tenantBotApiClients.size} API clients.`,
			);
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : "Unknown error";

			if (errorMessage.includes("password authentication failed")) {
				this.logger.error(
					"Database authentication failed. Please verify DATABASE_URL credentials",
					error,
				);
			} else if (errorMessage.includes("ECONNREFUSED")) {
				this.logger.error(
					"Could not connect to database. Please verify database is running and accessible",
					error,
				);
			} else {
				this.logger.error(
					"Failed to load active bot configurations or initialize clients:",
					error,
				);
			}

			// Instead of throwing, we'll return empty to allow the service to continue
			// with limited functionality rather than crashing completely
			return;
		}
	}

	/**
	 * Process an update received from the webhook for a specific path segment
	 * @param webhookPathSegment The webhook path segment
	 * @param update The Telegram update
	 * @returns Promise resolving when the update processing is complete
	 */
	async processUpdateForSegment(
		webhookPathSegment: string,
		update: TelegramUpdate,
	): Promise<void> {
		let botConfig = this.activeBotConfigs.get(webhookPathSegment);

		// If config not found (e.g., new bot added or config changed), attempt reload
		if (!botConfig) {
			this.logger.warn(
				`No cached config for segment: ${webhookPathSegment}. Attempting reload.`,
			);
			await this.loadActiveBotConfigsAndInitializeClients(); // Attempt a full reload
			botConfig = this.activeBotConfigs.get(webhookPathSegment);

			// If still not found after reload, it's likely an invalid segment or disabled bot
			if (!botConfig) {
				const errorMessage = `Bot configuration not found or not enabled for segment: ${webhookPathSegment} after reload.`;
				this.logger.error(errorMessage);
				// Important: Respond to Telegram with an error or 404 status code
				// This is handled in the controller by throwing this error
				throw new Error(errorMessage);
			}
			this.logger.log(
				`Successfully loaded config for segment: ${webhookPathSegment} after reload.`,
			);
		}

		// Attach the resolved (and decrypted token) botConfig to the update object.
		// The `derive` method will use this to find the correct tenant context and API client.
		// GramIO allows adding arbitrary properties to the update object this way.
		(update as any)._botConfig = botConfig;

		// Ensure the universal processor is initialized before processing
		if (!this.universalGramioProcessor) {
			// This indicates a startup issue if onModuleInit failed
			const errorMessage = "Universal GramIO processor not initialized.";
			this.logger.error(errorMessage);
			throw new Error(errorMessage);
		}

		// Process the update using the single universal processor instance
		// The `derive` function will inject the correct tenant context and API client
		// Cast update to any because TelegramUpdate type from GramIO might not include the _botConfig property signature
		// Cast universalGramioProcessor to any to access handleUpdate method
		await (this.universalGramioProcessor as any).handleUpdate(update as any);
		this.logger.debug(
			`Successfully processed update ${update.update_id} for segment ${webhookPathSegment}`,
		);
	}

	// --- Token Encryption/Decryption ---
	// Implement proper encryption/decryption for bot tokens using a secure key
	// The key should be stored securely (e.g., environment variables managed by a secret manager)

	private decryptToken(encryptedToken: string): string {
		const encryptionKey = this.config.tokenEncryptionKey;
		// Ensure encryptionKey is valid before attempting decryption logic
		if (!encryptionKey || Buffer.from(encryptionKey, "hex").length !== 32) {
			this.logger.error(
				"TOKEN_ENCRYPTION_KEY is missing or invalid. Assuming token is plain text (INSECURE IN PRODUCTION).",
			);
			return encryptedToken; // If key is bad, assume it was never encrypted
		}
		try {
			const parts = encryptedToken.split(":");
			if (parts.length !== 3) {
				// Not in expected IV:AuthTag:Ciphertext format, might be plain text or invalid
				this.logger.warn(
					"Token is not in expected encrypted format. Assuming plain text.",
				);
				return encryptedToken;
			}
			const [ivHex, authTagHex, encryptedData] = parts;
			const iv = Buffer.from(ivHex, "hex");
			const authTag = Buffer.from(authTagHex, "hex");
			// Ensure key is a Buffer of correct length
			const keyBuffer = Buffer.from(encryptionKey, "hex");
			if (keyBuffer.length !== 32) {
				// This case should be caught by the initial check, but double-checking is safe
				throw new Error("Invalid encryption key length");
			}
			const decipher = crypto.createDecipheriv("aes-256-gcm", keyBuffer, iv);
			decipher.setAuthTag(authTag);
			let decrypted = decipher.update(encryptedData, "hex", "utf8");
			decrypted += decipher.final("utf8");
			return decrypted;
		} catch (error: any) {
			// Log decryption errors but return the original string, as it might be valid plain text
			this.logger.error(
				`Failed to decrypt token: ${error.message}. Returning original string.`,
			);
			return encryptedToken; // Return the input string on error
		}
	}

	// User roles are now fetched directly from the DatabaseService in the derive function
}
