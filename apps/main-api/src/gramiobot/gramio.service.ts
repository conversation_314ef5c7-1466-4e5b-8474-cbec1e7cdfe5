// src/gramiobot/gramio.service.ts
import { autoAnswerCallbackQuery } from "@gramio/auto-answer-callback-query";
import { autoRetry } from "@gramio/auto-retry";
import { mediaCache } from "@gramio/media-cache";
import { mediaGroup } from "@gramio/media-group";
import { prompt } from "@gramio/prompt";
import { scenes } from "@gramio/scenes";
import { session } from "@gramio/session";
import { redisStorage } from "@gramio/storage-redis";
import {
	Inject,
	Injectable,
	Logger,
	type OnModuleDestroy,
	type OnModuleInit,
	forwardRef,
} from "@nestjs/common";
// Removed token imports as we are switching to class-based injection for AppConfigService and RedisService

import { Bot as GramioBotClass } from "gramio";

// Import handler services
import { CallbackQueryHandlersService } from "../commands/callback-query-handlers.service.js";
import { ChosenInlineResultHandlersService } from "../commands/chosen-inline-result-handlers.service.js";
import { CommandHandlersService } from "../commands/command-handlers.service.js";
import { InlineQueryHandlersService } from "../commands/inline-query-handlers.service.js";
import { ReactionHandlersService } from "../commands/reaction-handlers.service.js";
import { StartCommandHandlersService } from "../commands/start-handlers.service.js";
import { SceneCommandHandlersService } from "../scenes/scenes-command-handlers.service.js";

import type { RedisService } from "../redis/redis.service.js"; // Uncommented for Redis integration

import { greetingScene } from "../scenes/greeting.scene.js";
import { i18n } from "@/shared/locales/index.js";
import type { AppBot, AppBotErrorDefinitions } from "../types/bot-context.js";

@Injectable()
export class BotService implements OnModuleInit, OnModuleDestroy {
	private readonly logger = new Logger(BotService.name);
	public bot!: AppBot;

	private botInitializationPromise: Promise<AppBot>;
	private resolveBotInitialized!: (bot: AppBot) => void;
	private rejectBotInitialized!: (reason?: any) => void;

	constructor(
		// Switched to class-based injection for AppConfigService and RedisService
		@Inject("BotConfig")
		private readonly botConfig: { botToken: string },
		private readonly redisService: RedisService, // Uncommented for Redis integration

		// These injections are still fine, BotService needs these services
		@Inject(forwardRef(() => CommandHandlersService))
		private readonly commandHandlers: CommandHandlersService,
		@Inject(forwardRef(() => CallbackQueryHandlersService))
		private readonly callbackQueryHandlers: CallbackQueryHandlersService,
		@Inject(forwardRef(() => StartCommandHandlersService))
		private readonly startCommandHandlers: StartCommandHandlersService,
		@Inject(forwardRef(() => InlineQueryHandlersService))
		private readonly inlineQueryHandlers: InlineQueryHandlersService,
		@Inject(forwardRef(() => ReactionHandlersService))
		private readonly reactionHandlers: ReactionHandlersService,
		@Inject(forwardRef(() => ChosenInlineResultHandlersService))
		private readonly chosenInlineResultHandlers: ChosenInlineResultHandlersService,
		@Inject(forwardRef(() => SceneCommandHandlersService))
		private readonly sceneCommandHandlers: SceneCommandHandlersService,
	) {
		this.botInitializationPromise = new Promise((resolve, reject) => {
			this.resolveBotInitialized = resolve;
			this.rejectBotInitialized = reject;
		});
	}

	async onModuleInit() {
		try {
			this.logger.log("BotService onModuleInit started.");
			const storage = redisStorage(this.redisService.client); // Switched to Redis storage
			// const storage = memoryStorage(); // Using in-memory storage for scenes
			const allScenes = [greetingScene];

			const botInstance = new GramioBotClass<AppBotErrorDefinitions>(
				this.botConfig.botToken,
			)
				.extend(autoAnswerCallbackQuery())
				.extend(mediaGroup())
				.extend(autoRetry())
				.extend(mediaCache())
				.extend(session())
				.extend(scenes(allScenes, { storage }))
				.extend(prompt())
				.derive(
					["message", "callback_query", "inline_query", "chosen_inline_result"],
					(ctx: any) => {
						const lang = ctx.from?.languageCode;
						return { t: i18n.buildT(lang) };
					},
				)
				.onStart(({ info }) =>
					this.logger.log(`✨ Bot ${info.username} was started!`),
				);

			this.bot = botInstance as unknown as AppBot; // Assign to the class property with type assertion
			this.logger.log("Bot instance created in BotService.");
			this.resolveBotInitialized(this.bot); // Resolve the promise for the Bot factory

			// === Explicitly initialize handler services with the bot instance ===
			this.logger.log("Initializing handler services...");
			this.commandHandlers.initialize(this.bot);
			this.callbackQueryHandlers.initialize(this.bot);
			this.inlineQueryHandlers.initialize(this.bot);
			this.reactionHandlers.initialize(this.bot);
			this.chosenInlineResultHandlers.initialize(this.bot);
			this.startCommandHandlers.initialize(this.bot);
			if (this.sceneCommandHandlers) {
				this.sceneCommandHandlers.initialize(this.bot);
			}
			this.logger.log("Handler services initialized.");

			// === Register handlers (they will now use their internal botInstance) ===
			this.logger.log("Registering handlers on bot instance...");
			this.commandHandlers.registerHandlers();
			this.callbackQueryHandlers.registerHandlers();
			this.inlineQueryHandlers.registerHandlers();
			this.reactionHandlers.registerHandlers();
			this.chosenInlineResultHandlers.registerHandlers();
			this.startCommandHandlers.registerHandlers();
			if (this.sceneCommandHandlers) {
				this.sceneCommandHandlers.registerHandlers();
			}
			this.logger.log("All handlers registered.");

			await this.bot.start();
			this.logger.log("Bot started successfully.");
		} catch (error) {
			this.logger.error("Failed to initialize or start the bot:", error);
			if (this.rejectBotInitialized) {
				this.rejectBotInitialized(error);
			}
			throw error;
		}
	}

	public getInitializedBot(): Promise<AppBot> {
		return this.botInitializationPromise;
	}

	async onModuleDestroy() {
		if (this.bot) {
			await this.bot.stop();
			this.logger.log("Bot stopped");
		}
	}
}
