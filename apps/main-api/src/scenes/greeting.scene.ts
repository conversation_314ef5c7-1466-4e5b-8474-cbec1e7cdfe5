import { Scene } from "@gramio/scenes";

// Define the scene state interface
interface GreetingSceneState {
	name?: string;
	age?: number;
}

// Create the scene
export const greetingScene = new Scene("greeting")
	.state<GreetingSceneState>()

	// First step: Ask for name
	.step("message", (ctx) => {
		if (ctx.scene.step.firstTime) {
			return ctx.send("Please enter your name:");
		}

		if (!ctx.text) {
			return ctx.send("Please enter a valid name.");
		}

		return ctx.scene.update({
			name: ctx.text,
		});
	})

	// Second step: Ask for age
	.step("message", (ctx) => {
		if (ctx.scene.step.firstTime) {
			return ctx.send("Please enter your age:");
		}

		const age = Number(ctx.text);
		if (!ctx.text || Number.isNaN(age)) {
			return ctx.send("Please enter a valid age.");
		}

		return ctx.scene.update({
			age,
		});
	})

	// Final step: Show summary and exit
	.step("message", async (ctx) => {
		const name = ctx.scene.state.name ?? "Anonymous";
		const age = ctx.scene.state.age ?? 0;

		// Send a summary message
		await ctx.send(`Thank you, ${name}! You are ${age} years old.`);

		return ctx.scene.exit();
	});
