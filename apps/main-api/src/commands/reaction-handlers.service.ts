// src/commands/reaction-handlers.service.ts
import { Injectable, Logger } from "@nestjs/common";
import type {
	AppBot,
	AppMessageReactionContext,
} from "../types/bot-context.js";

/**
 * Service providing strictly-typed, modular, and localized Telegram reaction handlers.
 */
@Injectable()
export class ReactionHandlersService {
	private readonly logger = new Logger(ReactionHandlersService.name);

	private botInstance!: AppBot;

	public initialize(bot: AppBot): void {
		this.botInstance = bot;
	}

	registerHandlers(): void {
		this.logger.log("Registering reaction handlers...");
		// Assuming reactions are on messages, which would have BasicSceneMethods via the scenes plugin
		this.botInstance.reaction("👍", (ctx: any) =>
			this.handleThumbsUpReaction(ctx as AppMessageReactionContext),
		);
		this.logger.debug("Thumbs up reaction handler registered.");
	}

	private async handleThumbsUpReaction(
		ctx: AppMessageReactionContext,
	): Promise<void> {
		// Access standard MessageReactionContext properties:
		const userId = ctx.user?.id;
		const reactedMessageId = ctx.payload.message_id;
		const chatId = ctx.chat.id;

		this.logger.verbose(
			`[reaction] Thumbs up by user ${userId} on message ${reactedMessageId} in chat ${chatId}`,
		);

		// Access derived properties (they are optional on AppMessageReactionContext via AppBaseContext)
		if (ctx.chat?.id && ctx.user) {
			try {
				const thankYouMessage = ctx.t("thankYou") || "Thanks!";
				await ctx.tenantBotApiClient.sendMessage({
					chat_id: chatId,
					text: thankYouMessage,
				});
			} catch (e) {
				this.logger.error("Failed to send thank you for reaction", e);
			}
		} else {
			this.logger.warn(
				"Derived context properties (t or tenantBotApiClient) missing in reaction handler.",
			);
		}
	}
}
