import { db, type DbType } from "@repo/db"; // Import db directly and DbType
import { Global, Module, type Provider } from '@nestjs/common';
import { DatabaseService } from "./database.service.js";
import { ConfigModule } from "../config/config.module.js";

@Global()
@Module({
	imports: [ConfigModule],
	providers: [
		{
			provide: "DRIZZLE_INSTANCE", // Use a string token for the Drizzle instance
			useValue: db, // Use the imported db instance, which is typed as DbType
		} as Provider<DbType>, // Explicitly type the provider
		DatabaseService,
	],
	exports: [DatabaseService, "DRIZZLE_INSTANCE"],
})
export class DatabaseModule {}
