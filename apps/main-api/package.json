{"name": "@repo/main-api", "version": "0.0.1", "description": "Telegram bot API service", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "start": "node dist/main.js", "dev": "nest start --watch", "format": "biome format src --write", "lint": "biome lint src", "typecheck": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "clean": "rm -rf dist .turbo tsconfig.tsbuildinfo"}, "dependencies": {"@golevelup/nestjs-rabbitmq": "^6.0.0", "@gramio/auto-answer-callback-query": "^0.0.2", "@gramio/auto-retry": "^0.0.3", "@gramio/autoload": "^1.1.0", "@gramio/i18n": "^1.3.0", "@gramio/keyboards": "^1.2.1", "@gramio/media-cache": "^0.0.4", "@gramio/media-group": "^0.0.4", "@gramio/prompt": "^1.1.4", "@gramio/scenes": "^0.3.0", "@gramio/session": "^0.1.6", "@gramio/storage": "^1.0.0", "@gramio/storage-redis": "^1.0.4", "@repo/db": "workspace:*", "@nestjs/common": "^11.1.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.1", "@nestjs/microservices": "^11.1.2", "@nestjs/platform-express": "^11.1.1", "@verrou/core": "^0.5.1", "drizzle-orm": "^0.44.0", "env-var": "^7.5.0", "gramio": "^0.4.3", "ioredis": "^5.6.1", "nestjs-pino": "^4.4.0", "pino": "^9.7.0", "pino-http": "^10.4.0", "pino-pretty": "^13.0.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2"}, "devDependencies": {"@gramio/types": "^9.0.3", "@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.1", "@repo/typescript-config": "workspace:*", "@swc/core": "^1.11.24", "@types/bun": "^1.2.13", "swc-loader": "^0.2.6", "typescript": "^5.8.3", "webpack-node-externals": "^3.0.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}