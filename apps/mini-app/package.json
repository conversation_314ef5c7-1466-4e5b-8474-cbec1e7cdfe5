{"name": "@repo/mini-app", "private": true, "version": "0.0.2", "description": "Telegram mini app built with SolidJS", "type": "module", "scripts": {"deploy": "gh-pages -d dist", "dev": "vite", "dev:https": "vite", "build": "tsc --noEmit && vite build", "start": "vite preview", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0 --fix", "typecheck": "tsc --noEmit", "format": "biome format src --write", "test": "echo 'No tests yet'", "preview": "vite preview", "predeploy": "bun run build", "clean": "rm -rf dist .turbo tsconfig.tsbuildinfo"}, "dependencies": {"@repo/db": "workspace:*", "@solidjs/router": "^0.15.3", "@telegram-apps/sdk-solid": "3.0.22", "@tonconnect/ui": "^2.1.0", "eruda": "^3.4.1", "solid-js": "^1.9.7"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "vite": "^6.3.5", "eslint-plugin-solid": "^0.14.5", "vite-plugin-solid": "^2.10.2", "vite-plugin-mkcert": "^1.17.6", "vite-tsconfig-paths": "^5.1.4", "typescript": "^5.8.3"}}