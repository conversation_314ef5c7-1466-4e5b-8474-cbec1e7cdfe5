{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "ES2022", "module": "NodeNext", "moduleResolution": "NodeNext", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "noEmit": true, "baseUrl": ".", "paths": {"@repo/db": ["packages/db/src/index.ts"], "@repo/db/*": ["packages/db/src/*"], "@repo/typescript-config/*": ["packages/typescript-config/*"], "@repo/eslint-config/*": ["packages/eslint-config/*"]}}, "include": [], "exclude": ["node_modules", "**/dist", "**/.next", "**/out", "**/.turbo"], "references": [{"path": "./packages/db"}, {"path": "./packages/eslint-config"}, {"path": "./apps/main-api"}, {"path": "./apps/dashboard"}, {"path": "./apps/mini-app"}]}