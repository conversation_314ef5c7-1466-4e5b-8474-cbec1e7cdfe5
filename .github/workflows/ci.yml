name: CI Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  BUN_VERSION: 1.2.3
  NODE_VERSION: 18

jobs:
  # Job 1: Code Quality and Linting
  quality:
    name: Code Quality
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: ${{ env.BUN_VERSION }}

      - name: Install dependencies
        run: bun install --frozen-lockfile

      - name: Run linting
        run: bun run lint

      - name: Run type checking
        run: bun run typecheck

      - name: Run formatting check
        run: bun run format:check

  # Job 2: Build and Test
  build-and-test:
    name: Build and Test
    runs-on: ubuntu-latest
    needs: quality
    strategy:
      matrix:
        package: [main-api, dashboard, mini-app, db]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: ${{ env.BUN_VERSION }}

      - name: Install dependencies
        run: bun install --frozen-lockfile

      - name: Build package
        run: bun run build --filter=@repo/${{ matrix.package }}

      - name: Run tests
        run: bun run test --filter=@repo/${{ matrix.package }}
        continue-on-error: true

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-${{ matrix.package }}
          path: |
            apps/${{ matrix.package }}/dist/
            apps/${{ matrix.package }}/.next/
            packages/${{ matrix.package }}/dist/
          retention-days: 1

  # Job 3: Docker Build and Test
  docker-build:
    name: Docker Build
    runs-on: ubuntu-latest
    needs: build-and-test
    strategy:
      matrix:
        service: [main-api, dashboard, mini-app, db-migrator]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: Dockerfile.${{ matrix.service }}
          push: false
          tags: taxi29-${{ matrix.service }}:test
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # Job 4: Integration Tests
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: docker-build
    services:
      postgres:
        image: postgis/postgis:latest
        env:
          POSTGRES_USER: taxi29
          POSTGRES_PASSWORD: test_password_123
          POSTGRES_DB: taxi29
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:latest
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: ${{ env.BUN_VERSION }}

      - name: Install dependencies
        run: bun install --frozen-lockfile

      - name: Run database migrations
        run: bun run db:migrations:apply
        env:
          DATABASE_URL: postgresql://taxi29:test_password_123@localhost:5432/taxi29

      - name: Run integration tests
        run: bun run test:integration
        env:
          DATABASE_URL: postgresql://taxi29:test_password_123@localhost:5432/taxi29
          REDIS_URL: redis://localhost:6379
        continue-on-error: true

  # Job 5: Security Scanning
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: quality
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

  # Job 6: Performance Tests
  performance:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: docker-build
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: ${{ env.BUN_VERSION }}

      - name: Install dependencies
        run: bun install --frozen-lockfile

      - name: Run performance tests
        run: bun run test:performance
        continue-on-error: true

  # Job 7: Notify Results
  notify:
    name: Notify Results
    runs-on: ubuntu-latest
    needs: [quality, build-and-test, docker-build, integration-tests, security]
    if: always()
    steps:
      - name: Notify success
        if: ${{ needs.quality.result == 'success' && needs.build-and-test.result == 'success' && needs.docker-build.result == 'success' }}
        run: echo "✅ All CI checks passed successfully!"

      - name: Notify failure
        if: ${{ needs.quality.result == 'failure' || needs.build-and-test.result == 'failure' || needs.docker-build.result == 'failure' }}
        run: echo "❌ Some CI checks failed. Please review the logs."
