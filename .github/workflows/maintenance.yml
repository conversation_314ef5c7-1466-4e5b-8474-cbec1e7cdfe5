name: Maintenance

on:
  schedule:
    # Run daily at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:

env:
  BUN_VERSION: 1.2.3

jobs:
  # Job 1: Dependency Updates
  dependency-updates:
    name: Check Dependency Updates
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: ${{ env.BUN_VERSION }}

      - name: Check for updates
        run: |
          echo "🔍 Checking for dependency updates..."
          bun update --dry-run > dependency-updates.txt
          cat dependency-updates.txt

      - name: Create dependency update issue
        if: github.event_name == 'schedule'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const updates = fs.readFileSync('dependency-updates.txt', 'utf8');
            
            if (updates.trim()) {
              github.rest.issues.create({
                owner: context.repo.owner,
                repo: context.repo.repo,
                title: `🔄 Dependency Updates Available - ${new Date().toISOString().split('T')[0]}`,
                body: `## Available Dependency Updates\n\n\`\`\`\n${updates}\n\`\`\`\n\nPlease review and update dependencies as needed.`,
                labels: ['dependencies', 'maintenance']
              });
            }

  # Job 2: Security Audit
  security-audit:
    name: Security Audit
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: ${{ env.BUN_VERSION }}

      - name: Install dependencies
        run: bun install --frozen-lockfile

      - name: Run security audit
        run: |
          echo "🔒 Running security audit..."
          bun audit || true

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'table'

  # Job 3: Code Quality Metrics
  code-quality:
    name: Code Quality Metrics
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: ${{ env.BUN_VERSION }}

      - name: Install dependencies
        run: bun install --frozen-lockfile

      - name: Generate code quality report
        run: |
          echo "📊 Generating code quality metrics..."
          
          # Count lines of code
          echo "## Lines of Code" > quality-report.md
          find . -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" | grep -v node_modules | xargs wc -l | tail -1 >> quality-report.md
          
          # Count test files
          echo "## Test Coverage" >> quality-report.md
          find . -name "*.test.*" -o -name "*.spec.*" | grep -v node_modules | wc -l >> quality-report.md
          
          # Check for TODO/FIXME comments
          echo "## Technical Debt" >> quality-report.md
          grep -r "TODO\|FIXME\|HACK" --include="*.ts" --include="*.tsx" --include="*.js" --include="*.jsx" . | grep -v node_modules | wc -l >> quality-report.md

      - name: Upload quality report
        uses: actions/upload-artifact@v4
        with:
          name: code-quality-report
          path: quality-report.md

  # Job 4: Performance Monitoring
  performance-check:
    name: Performance Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: ${{ env.BUN_VERSION }}

      - name: Install dependencies
        run: bun install --frozen-lockfile

      - name: Build all packages
        run: bun run build

      - name: Measure build performance
        run: |
          echo "⚡ Measuring build performance..."
          time bun run build > build-performance.txt 2>&1

      - name: Upload performance report
        uses: actions/upload-artifact@v4
        with:
          name: performance-report
          path: build-performance.txt

  # Job 5: Docker Image Cleanup
  docker-cleanup:
    name: Docker Image Cleanup
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule'
    steps:
      - name: Delete old container images
        uses: actions/delete-package-versions@v4
        with:
          package-name: ${{ github.repository }}-main-api
          package-type: container
          min-versions-to-keep: 5
          delete-only-untagged-versions: true

      - name: Delete old dashboard images
        uses: actions/delete-package-versions@v4
        with:
          package-name: ${{ github.repository }}-dashboard
          package-type: container
          min-versions-to-keep: 5
          delete-only-untagged-versions: true

      - name: Delete old mini-app images
        uses: actions/delete-package-versions@v4
        with:
          package-name: ${{ github.repository }}-mini-app
          package-type: container
          min-versions-to-keep: 5
          delete-only-untagged-versions: true

  # Job 6: Health Check
  health-check:
    name: Production Health Check
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule'
    steps:
      - name: Check production services
        run: |
          echo "🏥 Checking production service health..."
          
          # Check main API
          if curl -f https://api.taxi29.example.com/health; then
            echo "✅ Main API is healthy"
          else
            echo "❌ Main API health check failed"
            exit 1
          fi
          
          # Check dashboard
          if curl -f https://taxi29.example.com; then
            echo "✅ Dashboard is healthy"
          else
            echo "❌ Dashboard health check failed"
            exit 1
          fi

      - name: Create health check issue on failure
        if: failure()
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: `🚨 Production Health Check Failed - ${new Date().toISOString()}`,
              body: `## Production Health Check Failed\n\nOne or more production services are not responding correctly.\n\nPlease investigate immediately.`,
              labels: ['bug', 'production', 'urgent']
            });

  # Job 7: Backup Verification
  backup-verification:
    name: Backup Verification
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule'
    steps:
      - name: Verify database backups
        run: |
          echo "💾 Verifying database backups..."
          # Add backup verification logic here

      - name: Verify file backups
        run: |
          echo "📁 Verifying file backups..."
          # Add file backup verification logic here
