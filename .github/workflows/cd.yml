name: CD Pipeline

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}
  BUN_VERSION: 1.2.3

jobs:
  # Job 1: Build and Push Docker Images
  build-and-push:
    name: Build and Push Images
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    strategy:
      matrix:
        service: [main-api, dashboard, mini-app, db-migrator]
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-${{ matrix.service }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,prefix={{branch}}-

      - name: Build and push Docker image
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          file: Dockerfile.${{ matrix.service }}
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64,linux/arm64

  # Job 2: Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: build-and-push
    if: github.ref == 'refs/heads/main' || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'staging')
    environment:
      name: staging
      url: https://staging.taxi29.example.com
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy to staging
        run: |
          echo "🚀 Deploying to staging environment..."
          echo "Image tag: ${{ needs.build-and-push.outputs.image-tag }}"
          # Add your staging deployment commands here
          # For example, using docker-compose or Kubernetes

      - name: Run smoke tests
        run: |
          echo "🧪 Running smoke tests on staging..."
          # Add smoke tests here
          sleep 30
          curl -f https://staging-api.taxi29.example.com/health || exit 1
          curl -f https://staging.taxi29.example.com || exit 1

      - name: Notify staging deployment
        run: echo "✅ Successfully deployed to staging!"

  # Job 3: Deploy to Production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build-and-push, deploy-staging]
    if: startsWith(github.ref, 'refs/tags/v') || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production')
    environment:
      name: production
      url: https://taxi29.example.com
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy to production
        run: |
          echo "🚀 Deploying to production environment..."
          echo "Image tag: ${{ needs.build-and-push.outputs.image-tag }}"
          # Add your production deployment commands here

      - name: Run production health checks
        run: |
          echo "🏥 Running production health checks..."
          sleep 60
          curl -f https://api.taxi29.example.com/health || exit 1
          curl -f https://taxi29.example.com || exit 1

      - name: Notify production deployment
        run: echo "🎉 Successfully deployed to production!"

  # Job 4: Database Migration (Production)
  migrate-production:
    name: Migrate Production Database
    runs-on: ubuntu-latest
    needs: build-and-push
    if: startsWith(github.ref, 'refs/tags/v')
    environment:
      name: production-db
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run database migrations
        run: |
          echo "🗄️ Running production database migrations..."
          # Add database migration commands here
          # docker run --rm -e DATABASE_URL=${{ secrets.PROD_DATABASE_URL }} \
          #   ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-db-migrator:${{ github.sha }}

      - name: Verify migrations
        run: |
          echo "✅ Database migrations completed successfully!"

  # Job 5: Rollback (Manual)
  rollback:
    name: Rollback Deployment
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'rollback'
    environment:
      name: production
    steps:
      - name: Rollback deployment
        run: |
          echo "🔄 Rolling back deployment..."
          # Add rollback commands here

      - name: Verify rollback
        run: |
          echo "✅ Rollback completed successfully!"

  # Job 6: Cleanup
  cleanup:
    name: Cleanup Old Images
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()
    steps:
      - name: Delete old container images
        uses: actions/delete-package-versions@v4
        with:
          package-name: ${{ env.IMAGE_NAME }}-main-api
          package-type: container
          min-versions-to-keep: 10
          delete-only-untagged-versions: true

  # Job 7: Monitoring and Alerts
  monitoring:
    name: Setup Monitoring
    runs-on: ubuntu-latest
    needs: deploy-production
    if: startsWith(github.ref, 'refs/tags/v')
    steps:
      - name: Setup monitoring alerts
        run: |
          echo "📊 Setting up monitoring and alerts..."
          # Add monitoring setup commands here

      - name: Send deployment notification
        run: |
          echo "📢 Sending deployment notifications..."
          # Add notification commands here (Slack, Discord, etc.)
