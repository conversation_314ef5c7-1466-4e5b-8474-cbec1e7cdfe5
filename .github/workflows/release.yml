name: Release

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to release (e.g., v1.2.3)'
        required: true
        type: string

env:
  BUN_VERSION: 1.2.3

jobs:
  # Job 1: Validate Release
  validate-release:
    name: Validate Release
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.version.outputs.version }}
      is-prerelease: ${{ steps.version.outputs.is-prerelease }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Extract version
        id: version
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            VERSION="${{ github.event.inputs.version }}"
          else
            VERSION=${GITHUB_REF#refs/tags/}
          fi
          
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          
          if [[ $VERSION == *"-"* ]]; then
            echo "is-prerelease=true" >> $GITHUB_OUTPUT
          else
            echo "is-prerelease=false" >> $GITHUB_OUTPUT
          fi
          
          echo "Release version: $VERSION"

      - name: Validate version format
        run: |
          VERSION="${{ steps.version.outputs.version }}"
          if [[ ! $VERSION =~ ^v[0-9]+\.[0-9]+\.[0-9]+(-[a-zA-Z0-9]+)?$ ]]; then
            echo "❌ Invalid version format: $VERSION"
            echo "Expected format: v1.2.3 or v1.2.3-beta"
            exit 1
          fi
          echo "✅ Version format is valid"

  # Job 2: Run Full Test Suite
  test-suite:
    name: Full Test Suite
    runs-on: ubuntu-latest
    needs: validate-release
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: ${{ env.BUN_VERSION }}

      - name: Install dependencies
        run: bun install --frozen-lockfile

      - name: Run all tests
        run: |
          echo "🧪 Running full test suite for release..."
          bun run test
          bun run lint
          bun run typecheck
          bun run build

  # Job 3: Generate Release Notes
  generate-release-notes:
    name: Generate Release Notes
    runs-on: ubuntu-latest
    needs: validate-release
    outputs:
      release-notes: ${{ steps.notes.outputs.release-notes }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Generate release notes
        id: notes
        run: |
          VERSION="${{ needs.validate-release.outputs.version }}"
          
          # Get the previous tag
          PREVIOUS_TAG=$(git describe --tags --abbrev=0 HEAD^ 2>/dev/null || echo "")
          
          echo "## 🚀 What's New in $VERSION" > release-notes.md
          echo "" >> release-notes.md
          
          if [ -n "$PREVIOUS_TAG" ]; then
            echo "### 📝 Changes since $PREVIOUS_TAG" >> release-notes.md
            echo "" >> release-notes.md
            
            # Get commits since last tag
            git log --pretty=format:"- %s (%h)" $PREVIOUS_TAG..HEAD >> release-notes.md
          else
            echo "### 📝 Initial Release" >> release-notes.md
            echo "" >> release-notes.md
            echo "- Initial release of Taxi29 monorepo" >> release-notes.md
          fi
          
          echo "" >> release-notes.md
          echo "### 🏗️ Architecture" >> release-notes.md
          echo "- **@repo/main-api**: NestJS Telegram bot API service" >> release-notes.md
          echo "- **@repo/dashboard**: Next.js admin dashboard" >> release-notes.md
          echo "- **@repo/mini-app**: SolidJS Telegram mini app" >> release-notes.md
          echo "- **@repo/db**: Database schema and utilities" >> release-notes.md
          
          echo "" >> release-notes.md
          echo "### 🐳 Docker Images" >> release-notes.md
          echo "- \`ghcr.io/${{ github.repository }}-main-api:$VERSION\`" >> release-notes.md
          echo "- \`ghcr.io/${{ github.repository }}-dashboard:$VERSION\`" >> release-notes.md
          echo "- \`ghcr.io/${{ github.repository }}-mini-app:$VERSION\`" >> release-notes.md
          echo "- \`ghcr.io/${{ github.repository }}-db-migrator:$VERSION\`" >> release-notes.md
          
          # Set output for GitHub release
          {
            echo 'release-notes<<EOF'
            cat release-notes.md
            echo EOF
          } >> $GITHUB_OUTPUT

      - name: Upload release notes
        uses: actions/upload-artifact@v4
        with:
          name: release-notes
          path: release-notes.md

  # Job 4: Build Release Assets
  build-assets:
    name: Build Release Assets
    runs-on: ubuntu-latest
    needs: [validate-release, test-suite]
    strategy:
      matrix:
        service: [main-api, dashboard, mini-app]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: ${{ env.BUN_VERSION }}

      - name: Install dependencies
        run: bun install --frozen-lockfile

      - name: Build service
        run: bun run build --filter=@repo/${{ matrix.service }}

      - name: Create release archive
        run: |
          VERSION="${{ needs.validate-release.outputs.version }}"
          SERVICE="${{ matrix.service }}"
          
          # Create archive based on service type
          if [ "$SERVICE" = "main-api" ]; then
            tar -czf "taxi29-$SERVICE-$VERSION.tar.gz" -C apps/$SERVICE dist package.json
          elif [ "$SERVICE" = "dashboard" ]; then
            tar -czf "taxi29-$SERVICE-$VERSION.tar.gz" -C apps/$SERVICE .next package.json
          elif [ "$SERVICE" = "mini-app" ]; then
            tar -czf "taxi29-$SERVICE-$VERSION.tar.gz" -C apps/$SERVICE dist package.json
          fi

      - name: Upload release asset
        uses: actions/upload-artifact@v4
        with:
          name: taxi29-${{ matrix.service }}-${{ needs.validate-release.outputs.version }}
          path: taxi29-${{ matrix.service }}-${{ needs.validate-release.outputs.version }}.tar.gz

  # Job 5: Create GitHub Release
  create-release:
    name: Create GitHub Release
    runs-on: ubuntu-latest
    needs: [validate-release, test-suite, generate-release-notes, build-assets]
    permissions:
      contents: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download all artifacts
        uses: actions/download-artifact@v4

      - name: Create GitHub Release
        uses: actions/create-release@v1
        id: create_release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ needs.validate-release.outputs.version }}
          release_name: Release ${{ needs.validate-release.outputs.version }}
          body: ${{ needs.generate-release-notes.outputs.release-notes }}
          draft: false
          prerelease: ${{ needs.validate-release.outputs.is-prerelease }}

      - name: Upload release assets
        run: |
          VERSION="${{ needs.validate-release.outputs.version }}"
          
          # Upload each service archive
          for service in main-api dashboard mini-app; do
            if [ -f "taxi29-$service-$VERSION/taxi29-$service-$VERSION.tar.gz" ]; then
              curl -X POST \
                -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
                -H "Content-Type: application/gzip" \
                --data-binary @"taxi29-$service-$VERSION/taxi29-$service-$VERSION.tar.gz" \
                "${{ steps.create_release.outputs.upload_url }}?name=taxi29-$service-$VERSION.tar.gz"
            fi
          done

  # Job 6: Update Documentation
  update-docs:
    name: Update Documentation
    runs-on: ubuntu-latest
    needs: [validate-release, create-release]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Update version in README
        run: |
          VERSION="${{ needs.validate-release.outputs.version }}"
          sed -i "s/Version: .*/Version: $VERSION/" README.md

      - name: Update CHANGELOG
        run: |
          VERSION="${{ needs.validate-release.outputs.version }}"
          DATE=$(date +%Y-%m-%d)
          
          # Create or update CHANGELOG.md
          if [ ! -f CHANGELOG.md ]; then
            echo "# Changelog" > CHANGELOG.md
            echo "" >> CHANGELOG.md
          fi
          
          # Add new version entry
          sed -i "2i\\## [$VERSION] - $DATE\\n\\n${{ needs.generate-release-notes.outputs.release-notes }}\\n" CHANGELOG.md

      - name: Commit documentation updates
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add README.md CHANGELOG.md
          git commit -m "docs: update documentation for ${{ needs.validate-release.outputs.version }}" || exit 0
          git push

  # Job 7: Notify Release
  notify-release:
    name: Notify Release
    runs-on: ubuntu-latest
    needs: [validate-release, create-release]
    if: always()
    steps:
      - name: Notify success
        if: needs.create-release.result == 'success'
        run: |
          echo "🎉 Release ${{ needs.validate-release.outputs.version }} created successfully!"
          echo "📦 Release URL: https://github.com/${{ github.repository }}/releases/tag/${{ needs.validate-release.outputs.version }}"

      - name: Notify failure
        if: needs.create-release.result == 'failure'
        run: |
          echo "❌ Release ${{ needs.validate-release.outputs.version }} failed!"
          echo "Please check the workflow logs for details."
