{"name": "my-monorepo", "private": true, "packageManager": "bun@1.2.3", "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "start": "turbo run start", "prod": "turbo run start", "start:main-api": "turbo run start --filter=main-api", "start:dashboard": "turbo run start --filter=@monorepo/dashboard", "start:mini-app": "turbo run start --filter=@monorepo/mini-app", "dev:main-api": "turbo run dev --filter=main-api", "dev:dashboard": "turbo run dev --filter=@monorepo/dashboard", "dev:mini-app": "turbo run dev --filter=@monorepo/mini-app", "prod:main-api": "turbo run start --filter=main-api", "prod:dashboard": "turbo run start --filter=@monorepo/dashboard", "prod:mini-app": "turbo run start --filter=@monorepo/mini-app", "lint": "turbo run lint", "typecheck": "turbo run typecheck", "clean": "turbo run clean", "format": "turbo run format", "db:build": "turbo run build --filter=@monorepo/db", "db:migrations:generate": "turbo run build --filter=@monorepo/db && turbo run _generate_and_apply_migrations_interactive --filter=@monorepo/db", "db:migrations:apply": "turbo run _apply_migrations --filter=@monorepo/db", "db:seed": "turbo run _seed_database --filter=@monorepo/db", "db:reset:full": "echo 'INFO: Clearing old migration files (if any)...' && rm -rf packages/db/drizzle && bun run db:reset:drop-recreate && echo 'INFO: Generating initial migrations...' && bun run db:migrations:generate && echo 'INFO: Seeding database...' && bun run db:seed", "db:reset:drop-recreate": "turbo run _drop_recreate_db --filter=@monorepo/db", "db:kit:generate": "turbo run kit:generate --filter=@monorepo/db", "db:kit:push": "turbo run kit:push --filter=@monorepo/db", "db:kit:studio": "turbo run kit:studio --filter=@monorepo/db", "format:check": "biome check .", "format:fix": "biome check . --apply", "lint:biome": "biome lint .", "lint:biome:fix": "biome lint . --apply", "format:biome": "biome format .", "format:biome:fix": "biome format . --write", "db:check:exports": "turbo run check:exports --filter=@monorepo/db"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@swc/core": "^1.11.24", "swc-loader": "^0.2.6", "turbo": "^2.5.4"}, "dependencies": {"@types/node": "^22.15.29"}, "trustedDependencies": ["@biomejs/biome", "@nestjs/core", "@swc/core", "core-js", "esbuild", "nest<PERSON><PERSON>-pino", "sharp", "unrs-resolver"]}