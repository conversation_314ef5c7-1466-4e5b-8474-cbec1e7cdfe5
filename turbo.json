{"$schema": "https://turborepo.com/schema.json", "globalDependencies": ["tsconfig.json", "packages/typescript-config/**", "bun.lock", "biome.json"], "tasks": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", ".next/**", "!.next/cache/**", "out/**", "public/registry/**"]}, "dev": {"dependsOn": ["^build"], "cache": false, "persistent": true}, "start": {"dependsOn": ["build"], "cache": false, "persistent": true}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "lint": {"dependsOn": ["^build"], "outputs": []}, "typecheck": {"dependsOn": ["^build"], "outputs": []}, "format": {"outputs": [], "cache": false}, "clean": {"cache": false}, "@repo/db#build": {"outputs": ["dist/**", "tsconfig.tsbuildinfo"]}, "@repo/main-api#build": {"dependsOn": ["@repo/db#build"], "outputs": ["dist/**"]}, "@repo/main-api#dev": {"dependsOn": ["@repo/db#build"], "cache": false, "persistent": true}, "@repo/main-api#start": {"dependsOn": ["@repo/main-api#build"], "cache": false, "persistent": true}, "@repo/dashboard#build": {"outputs": [".next/**", "!.next/cache/**", "out/**"]}, "@repo/dashboard#dev": {"cache": false, "persistent": true}, "@repo/dashboard#start": {"dependsOn": ["@repo/dashboard#build"], "cache": false, "persistent": true}, "@repo/mini-app#build": {"dependsOn": ["@repo/db#build"], "outputs": ["dist/**"]}, "@repo/mini-app#dev": {"dependsOn": ["@repo/db#build"], "cache": false, "persistent": true}, "@repo/mini-app#start": {"dependsOn": ["@repo/mini-app#build"], "cache": false, "persistent": true}, "registry:build": {"outputs": ["public/registry/**"]}, "_generate_and_apply_migrations_interactive": {"cache": false, "persistent": true}, "_apply_migrations": {"cache": false}, "_seed_database": {"cache": false}, "_drop_recreate_db": {"cache": false}, "kit:generate": {"cache": false}, "kit:push": {"cache": false}, "kit:studio": {"cache": false, "persistent": true}, "check:exports": {"cache": true}}}