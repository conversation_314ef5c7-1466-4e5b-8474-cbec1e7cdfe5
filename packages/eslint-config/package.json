{"name": "@repo/eslint-config", "version": "0.1.0", "private": true, "main": "library.js", "files": ["library.js", "next.js", "react-internal.js"], "scripts": {"lint": "echo 'Skipping lint for @repo/eslint-config'", "typecheck": "bunx tsc --noEmit -p ../../tsconfig.json"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^7.8.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-solid": "^0.14.0", "typescript": "^5.x"}, "dependencies": {"eslint-config-turbo": "^2.5.4", "eslint-plugin-only-warn": "^1.1.0"}}