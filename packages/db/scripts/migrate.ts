import { config } from "dotenv";
import { drizzle } from "drizzle-orm/postgres-js";
import { migrate } from "drizzle-orm/postgres-js/migrator";
import postgres from "postgres";

config();
const connectionString = process.env.DATABASE_URL;

if (!connectionString) {
	console.error("DATABASE_URL environment variable is not set");
	process.exit(1);
}

const sql = postgres(connectionString, { max: 1 });
const db = drizzle(sql);

async function main() {
	try {
		console.log("Running migrations...");
		await migrate(db, { migrationsFolder: "./drizzle" });
		console.log("Migrations completed successfully");
		await sql.end();
		process.exit(0);
	} catch (error) {
		console.error("Error running migrations:", error);
		await sql.end();
		process.exit(1);
	}
}

main();
