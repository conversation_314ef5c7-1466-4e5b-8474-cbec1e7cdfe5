// packages/db/scripts/check-exports.ts
import fs from "node:fs/promises";
import path from "node:path";
import process from "node:process";

const DB_PACKAGE_ROOT = path.join(process.cwd()); // Assumes script is run from packages/db root
// If running from monorepo root, adjust to:
// const DB_PACKAGE_ROOT = path.join(process.cwd(), "packages", "db");

const DIRECTORIES_TO_CHECK = [
  {
    name: "schema",
    dirPath: path.join(DB_PACKAGE_ROOT, "src", "schema"),
    indexPath: path.join(DB_PACKAGE_ROOT, "src", "schema", "index.ts"),
    expectedExtensionInExport: ".js", // As your index files export .js
  },
  {
    name: "enums",
    dirPath: path.join(DB_PACKAGE_ROOT, "src", "enums"),
    indexPath: path.join(DB_PACKAGE_ROOT, "src", "enums", "index.ts"),
    expectedExtensionInExport: ".js",
  },
];

interface MissingExport {
  directory: string;
  missingFile: string;
  expectedExport: string;
  indexPath: string;
}

async function checkDirectoryExports(
  dirPath: string,
  indexPath: string,
  expectedExtension: string,
  directoryName: string,
): Promise<MissingExport[]> {
  const missingExports: MissingExport[] = [];
  let indexContent = "";

  try {
    indexContent = await fs.readFile(indexPath, "utf-8");
  } catch (error) {
    console.error(`Error: Could not read index file at ${indexPath}. Skipping directory '${directoryName}'.`, error);
    // Optionally, add this as a critical error
    // missingExports.push({
    //   directory: directoryName,
    //   missingFile: `index.ts (itself)`,
    //   expectedExport: `N/A - index.ts not found or unreadable`,
    //   indexPath: indexPath
    // });
    return missingExports; // Or throw error to stop script
  }

  const files = await fs.readdir(dirPath);
  for (const file of files) {
    if (file === "index.ts" || !file.endsWith(".ts")) {
      continue;
    }

    const baseName = file.slice(0, -3); // Remove .ts
    const expectedExportFileName = `${baseName}${expectedExtension}`;
    // Adjusted to match your export pattern: export * from "./bots.schema.js";
    const expectedExportLine = `export * from "./${expectedExportFileName}";`;

    if (!indexContent.includes(expectedExportLine)) {
      missingExports.push({
        directory: directoryName,
        missingFile: file,
        expectedExport: expectedExportLine,
        indexPath,
      });
    }
  }
  return missingExports;
}

async function main() {
  console.log("Checking schema and enum exports in index.ts files...");
  let allMissingExports: MissingExport[] = [];
  let hasCriticalErrors = false;

  for (const dirConfig of DIRECTORIES_TO_CHECK) {
    try {
      await fs.access(dirConfig.dirPath); // Check if directory exists
      await fs.access(dirConfig.indexPath); // Check if index.ts exists
    } catch (error) {
      console.error(`Error: Directory or index.ts missing for '${dirConfig.name}'. Path: ${(error as any).path}`, error);
      console.error(`Please ensure both '${dirConfig.dirPath}' and '${dirConfig.indexPath}' exist.`);
      hasCriticalErrors = true;
      continue; // Skip this directory if essential files are missing
    }

    const missing = await checkDirectoryExports(
      dirConfig.dirPath,
      dirConfig.indexPath,
      dirConfig.expectedExtensionInExport,
      dirConfig.name,
    );
    allMissingExports = allMissingExports.concat(missing);
  }

  if (hasCriticalErrors) {
    console.error("\nCritical errors found (missing directories or index files). Please fix these first.");
    process.exit(1);
  }

  if (allMissingExports.length > 0) {
    console.error("\n❌ Found missing exports:");
    for (const missing of allMissingExports) {
      console.error(
        `- In '${path.relative(DB_PACKAGE_ROOT, missing.indexPath)}': File '${missing.missingFile}' is not exported. Expected line: \n    '${missing.expectedExport}'`
      );
    }
    process.exit(1);
  } else {
    console.log("\n✅ All schema and enum files are correctly exported in their respective index.ts files.");
    process.exit(0);
  }
}

main().catch((error) => {
  console.error("Script failed:", error);
  process.exit(1);
});