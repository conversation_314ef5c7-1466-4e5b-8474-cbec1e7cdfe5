import { exec } from "node:child_process";
import readline from "node:readline";
import { promisify } from "node:util";
import { config as loadEnvConfig } from "dotenv";
import { drizzle } from "drizzle-orm/postgres-js";
import { migrate } from "drizzle-orm/postgres-js/migrator";
import postgres from "postgres";

// Load environment variables
loadEnvConfig();

const execAsync = promisify(exec);

const connectionString = process.env.DATABASE_URL;

if (!connectionString) {
	console.error("DATABASE_URL environment variable is not set");
	process.exit(1);
}

async function main() {
	try {
		console.log(
			"Generating migrations (uses drizzle.config.ts, ensure 'db:build' was run if schema changed)...",
		);

		const { stdout, stderr } = await execAsync("npx drizzle-kit generate");

		if (
			stderr &&
			!stdout.includes("No schema changes detected") &&
			!stderr.toLowerCase().includes("experimental") &&
			!stderr.toLowerCase().includes("support drizzle")
		) {
			if (!stdout.trim()) {
				console.error("Error generating migrations:");
				if (stderr) console.error(stderr);
			} else {
				if (stderr) console.warn("Drizzle-kit output (stderr):\n", stderr);
			}
		}
		if (stdout) {
			console.log(stdout);
		}

		if (stdout.includes("No schema changes detected")) {
			console.log("No schema changes detected. Migrations not applied.");
			process.exit(0);
		}
		console.log("Migrations generation step completed.");

		const skipPrompt = process.argv.includes('--non-interactive');
		
		if (skipPrompt) {
			// Non-interactive mode: apply migrations without prompt
			await applyMigrations();
			process.exit(0);
		} else if (process.stdout.isTTY) {
			// Running in interactive terminal: show prompt
			const rl = readline.createInterface({
				input: process.stdin,
				output: process.stdout,
			});

			rl.question(
				"Do you want to apply the generated migrations? (y/n) ",
				async (answer: string) => {
					rl.close();

					if (answer.toLowerCase() === "y") {
						await applyMigrations();
					} else {
						console.log(
							"Migrations not applied. You can apply them later using the 'db:migrations:apply' script.",
						);
					}
					process.exit(0);
				},
			);
		} else {
			// Non-TTY environment, also apply migrations
			console.log("Running in non-TTY environment, applying migrations...");
			await applyMigrations();
			process.exit(0);
		}

		async function applyMigrations() {
			console.log("Applying migrations...");
			const sql = postgres(connectionString, { max: 1 });
			const db = drizzle(sql);
			try {
				await migrate(db, { migrationsFolder: "./drizzle" });
				console.log("Migrations applied successfully");
			} finally {
				await sql.end();
			}
		}
	} catch (error) {
		console.error("Error in migration generation/application process:", error);
		process.exit(1);
	}
}

main();
