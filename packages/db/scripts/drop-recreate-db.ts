// packages/db/scripts/drop-recreate-db.ts
import { config as loadEnv } from "dotenv";
import postgres from "postgres";

loadEnv(); // Load .env variables

const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

interface PostgresError extends Error {
  code?: string;
  routine?: string;
}

async function main() {
  const databaseUrl = process.env.DATABASE_URL;
  if (!databaseUrl) {
    console.error("DATABASE_URL environment variable is not set.");
    process.exit(1);
  }

  let dbName: string;
  let maintenanceConnectionString: string;
  let targetDbConnectionString: string;


  try {
    const url = new URL(databaseUrl);
    dbName = url.pathname.slice(1);
    if (!dbName) {
      throw new Error("Could not parse database name from DATABASE_URL.");
    }
    targetDbConnectionString = databaseUrl; // Save for connecting to the new DB

    url.pathname = "/postgres"; // Connect to a maintenance DB
    maintenanceConnectionString = url.toString();

  } catch (e) {
    console.error("Invalid DATABASE_URL:", (e as Error).message);
    process.exit(1);
  }

  const sqlMaintenance = postgres(maintenanceConnectionString, {
    max: 1,
    onnotice: (notice) => {
      console.log(`PostgreSQL Notice: ${notice.message} (Code: ${notice.code}, Severity: ${notice.severity})`);
    }
  });

  try {
    console.log(`Attempting to drop database: "${dbName}"...`);
    await sqlMaintenance.unsafe(`
      SELECT pg_terminate_backend(pg_stat_activity.pid)
      FROM pg_stat_activity
      WHERE pg_stat_activity.datname = '${dbName}'
        AND pid <> pg_backend_pid();
    `).catch(err => {
      console.warn(`Warning terminating connections to "${dbName}": ${err.message}. Proceeding with drop.`);
    });
    await delay(1000);

    await sqlMaintenance.unsafe(`DROP DATABASE IF EXISTS "${dbName}" WITH (FORCE);`);
    console.log(`Database "${dbName}" drop command issued.`);
    await delay(2000);

    console.log(`Attempting to create database: "${dbName}"...`);
    await sqlMaintenance.unsafe(`CREATE DATABASE "${dbName}";`);
    console.log(`Database "${dbName}" created successfully.`);

    // Connect to the newly created database to enable PostGIS
    await sqlMaintenance.end(); // Close maintenance connection

    const sqlTargetDb = postgres(targetDbConnectionString, { max: 1 });
    try {
      console.log(`Enabling PostGIS in database "${dbName}"...`);
      await sqlTargetDb.unsafe('CREATE EXTENSION IF NOT EXISTS postgis;');
      console.log(`PostGIS extension enabled (or already exists) in "${dbName}".`);
    } catch (postgisError) {
      console.error(`Error enabling PostGIS in "${dbName}": ${(postgisError as Error).message}`);
      // Decide if this is a fatal error for your setup
    } finally {
      await sqlTargetDb.end();
    }

  } catch (error) {
    const err = error as PostgresError;
    console.error(`Error during database drop/create: ${err.message}`);
    if (err.code === '42P04' && err.message?.includes('already exists')) {
        console.warn(`Warning: Database "${dbName}" reported as already existing during create.`);
    } else if (err.code === '23505' && err.message?.includes('pg_database_datname_index')) {
        console.warn(`Warning: Encountered 'pg_database_datname_index' violation for "${dbName}".`);
    }
    else if (err.message?.includes(`database "${dbName}" does not exist`) && err.routine === 'dropdb') {
        // Expected notice, handled by onnotice
    }
    else {
      // Consider if other errors should be fatal and uncomment process.exit(1)
    }
  } finally {
    // Ensure the maintenance connection is closed if it hasn't been already
    // sql.end() is idempotent and safe to call even if already ended or if it's a pool.
    await sqlMaintenance.end().catch(e => console.warn(`Error closing maintenance connection: ${(e as Error).message}`));
  }
}

main().catch((e) => {
  console.error("Script execution failed:", e instanceof Error ? e.message : String(e));
  process.exit(1);
});