CREATE TYPE "public"."promotion_type" AS ENUM('percentage', 'value', 'free_ride');--> statement-breakpoint
CREATE TYPE "public"."status" AS ENUM('RUNNING', 'STOPPED', 'ERROR');--> statement-breakpoint
CREATE TYPE "public"."verification_method" AS ENUM('sms', 'email', 'phone_call', 'other');--> statement-breakpoint
CREATE TYPE "public"."verification_status" AS ENUM('initiated', 'code_sent', 'verified', 'failed', 'expired');--> statement-breakpoint
CREATE TYPE "public"."actors_actor_type_enum" AS ENUM('USER', 'OPERATOR', 'SYSTEM');--> statement-breakpoint
CREATE TYPE "public"."billing_method" AS ENUM('CREDIT_CARD', 'PAYPAL', 'BANK_TRANSFER', 'CRYPTO');--> statement-breakpoint
CREATE TYPE "public"."billing_profiles_status_enum" AS ENUM('ACTIVE', 'INACTIVE', 'DEPRECATED');--> statement-breakpoint
CREATE TYPE "public"."calls_direction_enum" AS ENUM('INBOUND', 'OUTBOUND');--> statement-breakpoint
CREATE TYPE "public"."calls_status_enum" AS ENUM('RINGING', 'ANSWERED', 'MISSED', 'ENDED', 'FAILED');--> statement-breakpoint
CREATE TYPE "public"."events_event_type_enum" AS ENUM('MESSAGE_RECEIVED', 'MESSAGE_SENT', 'USER_LINKED', 'USER_UNLINKED', 'CONSENT_GRANTED', 'CONSENT_REVOKED', 'VERIFICATION_REQUESTED', 'VERIFICATION_SUCCEEDED', 'VERIFICATION_FAILED', 'SESSION_STARTED', 'SESSION_ENDED', 'ERROR', 'WEBHOOK_RECEIVED');--> statement-breakpoint
CREATE TYPE "public"."groups_group_status_enum" AS ENUM('ACTIVE', 'PENDING', 'INACTIVE');--> statement-breakpoint
CREATE TYPE "public"."groups_group_type_enum" AS ENUM('FRANCHISE', 'AGGREGATOR', 'BRAND', 'CORPORATE');--> statement-breakpoint
CREATE TYPE "public"."invoice_status" AS ENUM('PENDING', 'PAID', 'OPEN', 'FAILED');--> statement-breakpoint
CREATE TYPE "public"."kyc_status_enum" AS ENUM('pending', 'approved', 'rejected', 'expired');--> statement-breakpoint
CREATE TYPE "public"."message_direction" AS ENUM('IN', 'OUT');--> statement-breakpoint
CREATE TYPE "public"."message_type_enum" AS ENUM('TEXT', 'IMAGE', 'VOICE', 'LOCATION', 'VERIFICATION', 'DOCUMENT', 'STICKER', 'BUTTON', 'TEMPLATE', 'INTERACTIVE', 'CAROUSEL', 'GROUP_CHAT', 'UNKNOWN');--> statement-breakpoint
CREATE TYPE "public"."user_onboarding_status_enum" AS ENUM('INCOMPLETE', 'IN_PROGRESS', 'COMPLETED');--> statement-breakpoint
CREATE TYPE "public"."operators_operator_status_enum" AS ENUM('ACTIVE', 'INACTIVE', 'SUSPENDED', 'PENDING');--> statement-breakpoint
CREATE TYPE "public"."app_payment_method_enum" AS ENUM('CARD', 'WIRE', 'CRYPTO', 'INVOICE');--> statement-breakpoint
CREATE TYPE "public"."payment_status_enum" AS ENUM('pending', 'success', 'failed');--> statement-breakpoint
CREATE TYPE "public"."user_profile_change_type_enum" AS ENUM('ONBOARDING', 'PHONE_CHANGE', 'ADMIN_UPDATE');--> statement-breakpoint
CREATE TYPE "public"."promos_promo_status_enum" AS ENUM('ACTIVE', 'INACTIVE', 'EXPIRED', 'LIMIT_REACHED');--> statement-breakpoint
CREATE TYPE "public"."promo_type_enum" AS ENUM('PERCENTAGE', 'FIXED', 'FREE_RIDE');--> statement-breakpoint
CREATE TYPE "public"."provider_type_enum" AS ENUM('TELEGRAM', 'VIBER', 'FACEBOOK', 'GOOGLE', 'APPLE', 'PHONE');--> statement-breakpoint
CREATE TYPE "public"."ride_ratings_status_enum" AS ENUM('ONE', 'TWO', 'THREE', 'FOUR', 'FIVE');--> statement-breakpoint
CREATE TYPE "public"."ride_status_enum" AS ENUM('SEARCHING', 'ASSIGNED', 'PICKED_UP', 'COMPLETED', 'CANCELLED');--> statement-breakpoint
CREATE TYPE "public"."ride_type_enum" AS ENUM('RIDE', 'DELIVERY', 'POOLED', 'SHARED');--> statement-breakpoint
CREATE TYPE "public"."settings_setting_type_enum" AS ENUM('SYSTEM', 'PAYMENT', 'I18N', 'UI');--> statement-breakpoint
CREATE TYPE "public"."app_source_type_enum" AS ENUM('TELEGRAM', 'ANDROID', 'MQTT', 'WEB', 'MANUAL');--> statement-breakpoint
CREATE TYPE "public"."support_tickets_ticket_status_enum" AS ENUM('OPEN', 'CLOSED', 'PENDING');--> statement-breakpoint
CREATE TYPE "public"."tenants_tenant_plan_enum" AS ENUM('FREE', 'PRO', 'ENTERPRISE');--> statement-breakpoint
CREATE TYPE "public"."tenants_tenant_status_enum" AS ENUM('ACTIVE', 'SUSPENDED', 'CANCELLED');--> statement-breakpoint
CREATE TYPE "public"."user_role_enum" AS ENUM('PASSENGER', 'DRIVER', 'OPERATOR', 'MANAGER', 'ADMIN', 'SYSTEM_SUPPORT');--> statement-breakpoint
CREATE TYPE "public"."users_status_enum" AS ENUM('ACTIVE', 'INACTIVE', 'BANNED', 'SUSPENDED');--> statement-breakpoint
CREATE TYPE "public"."vehicles_vehicle_status_enum" AS ENUM('ACTIVE', 'DISABLED', 'DECOMMISSIONED', 'REPAIR');--> statement-breakpoint
CREATE TABLE "areas" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"name" varchar(255) NOT NULL,
	"geom" geometry(point) NOT NULL,
	"city" varchar(255),
	"country" varchar(2),
	"osm_tags" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "audit_logs" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"actor_id" uuid,
	"actor_type" "actors_actor_type_enum" NOT NULL,
	"tenant_id" uuid,
	"event_type" varchar(255) NOT NULL,
	"target_table" varchar(255),
	"target_id" uuid,
	"description" text,
	"details" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "bots" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"token" text NOT NULL,
	"webhook_path_segment" varchar(100) NOT NULL,
	"bot_username" varchar(255),
	"is_enabled" boolean DEFAULT true NOT NULL,
	"config" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "bots_webhook_path_segment_unique" UNIQUE("webhook_path_segment")
);
--> statement-breakpoint
CREATE TABLE "chat" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"type" varchar(50) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "chatbot_config" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"provider_id" uuid NOT NULL,
	"settings" jsonb NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "chatbot_events" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"chatbot_instance_id" uuid NOT NULL,
	"event_type" "events_event_type_enum" NOT NULL,
	"user_id" uuid,
	"session_id" uuid,
	"message_id" uuid,
	"details" jsonb NOT NULL,
	"occurred_at" timestamp DEFAULT now() NOT NULL,
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "chatbot_instance" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"provider_id" uuid NOT NULL,
	"name" varchar(100) NOT NULL,
	"bot_username" varchar(100),
	"webhook_url" varchar(1024),
	"config" jsonb,
	"is_active" boolean DEFAULT false NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "chatbot_message" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"chatbot_session_id" uuid NOT NULL,
	"chatbot_user_id" uuid NOT NULL,
	"direction" "message_direction" NOT NULL,
	"sent_at" timestamp NOT NULL,
	"message_type" "message_type_enum" NOT NULL,
	"content" text NOT NULL,
	"phone_verified" boolean DEFAULT false NOT NULL,
	"verification_reference" varchar(255),
	"replied_to_id" uuid,
	"error_flag" boolean DEFAULT false NOT NULL,
	"provider_message_id" varchar(255),
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "chatbot_message_with_geolocation" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"message_id" uuid NOT NULL,
	"location" geometry(point) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "chatbot_provider" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"name" varchar(50) NOT NULL,
	"description" varchar(500),
	"logo_url" varchar(1024),
	"features" jsonb,
	"enabled" boolean DEFAULT false NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "chatbot_session" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"chatbot_user_id" uuid NOT NULL,
	"tenant_id" uuid NOT NULL,
	"started_at" timestamp NOT NULL,
	"ended_at" timestamp,
	"verified" boolean DEFAULT false NOT NULL,
	"geolocation" geometry(point),
	"context" varchar(100),
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "chatbot_users" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"chatbot_instance_id" uuid NOT NULL,
	"provider_user_id" varchar(255),
	"phone_verified" boolean DEFAULT false NOT NULL,
	"verification_date" timestamp NOT NULL,
	"consent" boolean DEFAULT false NOT NULL,
	"consent_date" timestamp,
	"consent_revoked_at" timestamp,
	"blocked" boolean DEFAULT false NOT NULL,
	"verified_at" timestamp,
	"locale" varchar(10),
	"joined_at" timestamp DEFAULT now() NOT NULL,
	"last_seen_at" timestamp,
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "chatbot" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"provider" "provider_type_enum" NOT NULL,
	"bot_id" varchar(255) NOT NULL,
	"config" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "dispatch_assignment" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"operator_id" uuid NOT NULL,
	"ride_id" uuid NOT NULL,
	"assigned_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "driver_vehicle" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"driver_id" uuid NOT NULL,
	"vehicle_id" uuid NOT NULL,
	"from_time" timestamp NOT NULL,
	"to_time" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "geodata_entries" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid,
	"country" varchar(2),
	"city" varchar(255),
	"address" varchar(255),
	"geom" geometry(point) NOT NULL,
	"latitude" double precision NOT NULL,
	"longitude" double precision NOT NULL,
	"source" "app_source_type_enum" NOT NULL,
	"user_id" uuid,
	"accuracy" double precision,
	"area_id" uuid,
	"osm_tags" jsonb,
	"metadata" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "i18n_translations" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid,
	"namespace" varchar(100),
	"key" varchar(255) NOT NULL,
	"value" text NOT NULL,
	"locale" varchar(10) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "invoices" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"amount" numeric(10, 2) NOT NULL,
	"currency" varchar(3) NOT NULL,
	"status" "invoice_status" DEFAULT 'PENDING' NOT NULL,
	"due_date" timestamp NOT NULL,
	"issued_at" timestamp DEFAULT now() NOT NULL,
	"paid_at" timestamp,
	"metadata" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "map_provider" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"provider_name" varchar(50) NOT NULL,
	"access_token" varchar(255) NOT NULL,
	"refresh_token" varchar(255),
	"expires_at" timestamp,
	"is_active" boolean DEFAULT true NOT NULL,
	"config" json,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "messages" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"chat_id" uuid NOT NULL,
	"from_user_id" uuid NOT NULL,
	"to_user_id" uuid,
	"provider_message_id" varchar(255),
	"direction" "message_direction" NOT NULL,
	"via_channel" "provider_type_enum" NOT NULL,
	"message_type" "message_type_enum" NOT NULL,
	"content" text NOT NULL,
	"phone_verified" boolean,
	"verification_reference" varchar(255),
	"geolocation" geometry(point),
	"replied_to_message_id" uuid,
	"error_flag" boolean DEFAULT false,
	"metadata" jsonb,
	"sent_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "multi_tenant_group" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(255) NOT NULL,
	"type" "groups_group_type_enum" NOT NULL,
	"contact_email" varchar(255),
	"contact_phone" varchar(50),
	"parent_group_id" uuid,
	"settings" jsonb,
	"status" "groups_group_status_enum" DEFAULT 'PENDING' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "operator_extensions" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"user_tenant_id" uuid NOT NULL,
	"status" "operators_operator_status_enum" DEFAULT 'ACTIVE',
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "operator_shift" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"operator_id" uuid NOT NULL,
	"shift_start" timestamp NOT NULL,
	"shift_end" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "operators" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"user_tenant_id" uuid NOT NULL,
	"first_name" varchar(100) NOT NULL,
	"last_name" varchar(100) NOT NULL,
	"phone" varchar(20) NOT NULL,
	"status" "operators_operator_status_enum" DEFAULT 'ACTIVE' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "payment" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"ride_id" uuid,
	"user_id" uuid NOT NULL,
	"amount" numeric(10, 2) NOT NULL,
	"currency" varchar(3) NOT NULL,
	"method" "app_payment_method_enum" NOT NULL,
	"processor_ref" varchar(255),
	"status" "payment_status_enum" DEFAULT 'pending' NOT NULL,
	"processed_at" timestamp,
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "pbx_call" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"user_id" uuid,
	"operator_id" uuid,
	"ride_id" uuid,
	"direction" "calls_direction_enum" NOT NULL,
	"status" "calls_status_enum" NOT NULL,
	"duration" integer,
	"recording_url" varchar(1024),
	"started_at" timestamp NOT NULL,
	"ended_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "promos" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"code" varchar(20) NOT NULL,
	"description" varchar(255),
	"discount_value" integer NOT NULL,
	"discount_type" "promo_type_enum" NOT NULL,
	"max_uses" integer,
	"uses" integer DEFAULT 0 NOT NULL,
	"start_date" timestamp NOT NULL,
	"end_date" timestamp NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"status" "promos_promo_status_enum" DEFAULT 'ACTIVE' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "promotion" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"code" varchar(255) NOT NULL,
	"type" "promotion_type" NOT NULL,
	"value" numeric(10, 2) NOT NULL,
	"usage_limit" integer,
	"valid_from" timestamp,
	"valid_to" timestamp,
	"is_active" boolean DEFAULT true,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "ride_event" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"ride_id" uuid NOT NULL,
	"event_type" varchar(50) NOT NULL,
	"details" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "ride_order" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"passenger_id" uuid,
	"driver_id" uuid,
	"vehicle_id" uuid,
	"pickup_address" varchar(255),
	"pickup_latlng" jsonb,
	"dropoff_address" varchar(255),
	"dropoff_latlng" jsonb,
	"scheduled_time" timestamp,
	"confirmed_time" timestamp,
	"start_time" timestamp,
	"end_time" timestamp,
	"status" "ride_status_enum" DEFAULT 'SEARCHING' NOT NULL,
	"order_type" "ride_type_enum" DEFAULT 'RIDE' NOT NULL,
	"estimated_fare" numeric(10, 2),
	"currency" varchar(3),
	"payment_method" varchar(50),
	"paid" boolean DEFAULT false,
	"promo_id" uuid,
	"metadata" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "ride_rating" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"ride_id" uuid NOT NULL,
	"from_user_id" uuid NOT NULL,
	"to_user_id" uuid NOT NULL,
	"rating" integer NOT NULL,
	"feedback" text,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "rides" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"passenger_id" uuid NOT NULL,
	"driver_id" uuid NOT NULL,
	"vehicle_id" uuid NOT NULL,
	"status" "ride_status_enum" DEFAULT 'SEARCHING' NOT NULL,
	"order_type" "ride_type_enum" DEFAULT 'RIDE' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "roles" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"name" varchar(50) NOT NULL,
	"description" varchar(1024),
	"permissions" jsonb,
	"is_default" boolean DEFAULT false NOT NULL,
	"is_system" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE "support_ticket" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"user_id" uuid NOT NULL,
	"subject" varchar(256) NOT NULL,
	"details" text NOT NULL,
	"status" "support_tickets_ticket_status_enum" NOT NULL,
	"assigned_to_id" uuid,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"closed_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "system_settings" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid,
	"key" varchar(255) NOT NULL,
	"value" jsonb NOT NULL,
	"description" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "telegram_chats" (
	"id" bigint PRIMARY KEY NOT NULL,
	"type" varchar(32) NOT NULL,
	"title" varchar(256),
	"username" varchar(32),
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	"last_seen" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "telegram_messages" (
	"id" bigint PRIMARY KEY NOT NULL,
	"chat_id" bigint NOT NULL,
	"user_id" integer,
	"message_type" varchar(32),
	"text" varchar(4096),
	"file_id" varchar(256),
	"caption" varchar(1024),
	"reply_to_message_id" bigint,
	"forward_from_user_id" integer,
	"forward_from_chat_id" bigint,
	"edit_date" timestamp with time zone,
	"entities" json,
	"command" varchar(64),
	"payload" json,
	"service_type" varchar(32),
	"date" timestamp with time zone NOT NULL,
	"created_at" timestamp with time zone DEFAULT now(),
	"raw_data" json
);
--> statement-breakpoint
CREATE TABLE "telegram_users" (
	"id" bigint PRIMARY KEY NOT NULL,
	"first_name" varchar(255),
	"last_name" varchar(255),
	"username" varchar(255)
);
--> statement-breakpoint
CREATE TABLE "telegram_chat_members" (
	"chat_id" bigint NOT NULL,
	"user_id" integer NOT NULL,
	CONSTRAINT "telegram_chat_members_chat_id_user_id_pk" PRIMARY KEY("chat_id","user_id")
);
--> statement-breakpoint
CREATE TABLE "telegram_user_settings" (
	"user_id" integer PRIMARY KEY NOT NULL,
	"language" varchar(8),
	"notifications" boolean DEFAULT true
);
--> statement-breakpoint
CREATE TABLE "tenant_billing_profile" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"name" varchar(255) NOT NULL,
	"billing_email" varchar(255),
	"billing_phone" varchar(50),
	"tax_id" varchar(100),
	"address" varchar(512),
	"country" varchar(100),
	"currency" varchar(10),
	"payment_method" "app_payment_method_enum" NOT NULL,
	"default_profile" boolean DEFAULT false NOT NULL,
	"status" "billing_profiles_status_enum" DEFAULT 'ACTIVE' NOT NULL,
	"metadata" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "tenant_bots" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"bot_name" varchar(100),
	"telegram_bot_token" varchar(255) NOT NULL,
	"webhook_domain" varchar(255),
	"webhook_path" varchar(255),
	"webhook_secret_token" varchar(255),
	"status" "status" DEFAULT 'STOPPED' NOT NULL,
	"last_status_update" timestamp with time zone DEFAULT now() NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "tenant_localization" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"default_locale" varchar(10) NOT NULL,
	"supported_locales" jsonb NOT NULL,
	"timezone" varchar(64) NOT NULL,
	"currency_symbol" varchar(10) NOT NULL,
	"labels" jsonb NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "tenant_settings" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"key" varchar(255) NOT NULL,
	"value" jsonb,
	"type" "settings_setting_type_enum" NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "tenants" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(255) NOT NULL,
	"legal_name" varchar(255),
	"email" varchar(255),
	"phone" varchar(50),
	"logo_url" varchar(1024),
	"address" varchar(512),
	"country" varchar(2),
	"timezone" varchar(64),
	"plan" "tenants_tenant_plan_enum",
	"status" "tenants_tenant_status_enum" DEFAULT 'ACTIVE' NOT NULL,
	"multi_tenant_group_id" uuid,
	"metadata" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "user_bot_roles" (
	"telegram_user_id" bigint NOT NULL,
	"bot_id" uuid NOT NULL,
	"role_id" uuid NOT NULL,
	CONSTRAINT "user_bot_roles_telegram_user_id_bot_id_role_id_pk" PRIMARY KEY("telegram_user_id","bot_id","role_id")
);
--> statement-breakpoint
CREATE TABLE "user_consent" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"tenant_id" uuid,
	"consent_type" varchar(50) NOT NULL,
	"granted" boolean NOT NULL,
	"granted_at" timestamp DEFAULT now() NOT NULL,
	"revoked_at" timestamp,
	"deleted_at" timestamp,
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "user_identity" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"provider" "user_role_enum" NOT NULL,
	"external_id" varchar(255) NOT NULL,
	"display" varchar(255) NOT NULL,
	"avatar_url" varchar(1024),
	"metadata" json DEFAULT '{}'::json NOT NULL,
	"linked_at" timestamp DEFAULT now() NOT NULL,
	"unlinked_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "user_kyc" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid,
	"user_id" uuid NOT NULL,
	"provider" varchar(255) NOT NULL,
	"submitted_at" timestamp DEFAULT now() NOT NULL,
	"status" "kyc_status_enum" NOT NULL,
	"document_type" varchar(255) NOT NULL,
	"document_number" varchar(255) NOT NULL,
	"country" varchar(100) NOT NULL,
	"expiry_date" date,
	"rejection_reason" varchar(1024),
	"metadata" json
);
--> statement-breakpoint
CREATE TABLE "user_onboarding" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"tenant_id" uuid,
	"completed_steps" jsonb,
	"status" "user_onboarding_status_enum" NOT NULL,
	"completed_at" timestamp,
	"metadata" jsonb,
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "user_profile_history" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"changed_by_id" uuid,
	"change_type" "user_profile_change_type_enum" NOT NULL,
	"old_value" json NOT NULL,
	"new_value" json NOT NULL,
	"changed_at" timestamp DEFAULT now() NOT NULL,
	"context" varchar(50) NOT NULL,
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "user_tenant" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"tenant_id" uuid NOT NULL,
	"role_id" uuid NOT NULL,
	"status" "users_status_enum" DEFAULT 'ACTIVE' NOT NULL,
	"is_primary" boolean DEFAULT false NOT NULL,
	"last_used" timestamp,
	"registered_at" timestamp DEFAULT now() NOT NULL,
	"invited_by_id" uuid,
	"metadata" json DEFAULT '{}'::json NOT NULL
);
--> statement-breakpoint
CREATE TABLE "users" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"phone" varchar(20) NOT NULL,
	"email" varchar(255),
	"external_id" varchar(255),
	"verified" boolean DEFAULT false NOT NULL,
	"registered_at" timestamp DEFAULT now() NOT NULL,
	"last_login_at" timestamp,
	"legal_name" varchar(255),
	"display_name" varchar(255),
	"date_of_birth" timestamp,
	"language" varchar(10),
	"avatar_url" varchar(1024),
	"communication_opt_in" json,
	"privacy_flags" json,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"deleted_at" timestamp,
	"metadata" json,
	CONSTRAINT "users_phone_unique" UNIQUE("phone"),
	CONSTRAINT "users_email_unique" UNIQUE("email"),
	CONSTRAINT "users_external_id_unique" UNIQUE("external_id")
);
--> statement-breakpoint
CREATE TABLE "vehicle" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"license_plate" varchar(20) NOT NULL,
	"make" varchar(100) NOT NULL,
	"model" varchar(100) NOT NULL,
	"color" varchar(50),
	"year" integer,
	"registration_id" varchar(100),
	"status" "vehicles_vehicle_status_enum" DEFAULT 'ACTIVE' NOT NULL,
	"metadata" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "verification_event" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"chatbot_user_id" uuid NOT NULL,
	"method" "verification_method" NOT NULL,
	"status" "verification_status" NOT NULL,
	"reference" varchar(255),
	"initiated_at" timestamp DEFAULT now() NOT NULL,
	"completed_at" timestamp,
	"expires_at" timestamp,
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "wallet" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"tenant_id" uuid NOT NULL,
	"currency" varchar(3) NOT NULL,
	"balance" numeric(10, 2) DEFAULT '0.0' NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "webhook_subscriber" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"url" varchar(255) NOT NULL,
	"events" text NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "audit_logs" ADD CONSTRAINT "audit_logs_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "bots" ADD CONSTRAINT "bots_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chat" ADD CONSTRAINT "chat_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_config" ADD CONSTRAINT "chatbot_config_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_config" ADD CONSTRAINT "chatbot_config_provider_id_chatbot_provider_id_fk" FOREIGN KEY ("provider_id") REFERENCES "public"."chatbot_provider"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_events" ADD CONSTRAINT "chatbot_events_chatbot_instance_id_chatbot_instance_id_fk" FOREIGN KEY ("chatbot_instance_id") REFERENCES "public"."chatbot_instance"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_events" ADD CONSTRAINT "chatbot_events_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_events" ADD CONSTRAINT "chatbot_events_session_id_chatbot_session_id_fk" FOREIGN KEY ("session_id") REFERENCES "public"."chatbot_session"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_events" ADD CONSTRAINT "chatbot_events_message_id_chatbot_message_id_fk" FOREIGN KEY ("message_id") REFERENCES "public"."chatbot_message"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_instance" ADD CONSTRAINT "chatbot_instance_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_instance" ADD CONSTRAINT "chatbot_instance_provider_id_chatbot_provider_id_fk" FOREIGN KEY ("provider_id") REFERENCES "public"."chatbot_provider"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_message" ADD CONSTRAINT "chatbot_message_chatbot_session_id_chatbot_session_id_fk" FOREIGN KEY ("chatbot_session_id") REFERENCES "public"."chatbot_session"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_message" ADD CONSTRAINT "chatbot_message_chatbot_user_id_chatbot_users_id_fk" FOREIGN KEY ("chatbot_user_id") REFERENCES "public"."chatbot_users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_message_with_geolocation" ADD CONSTRAINT "chatbot_message_with_geolocation_message_id_chatbot_message_id_fk" FOREIGN KEY ("message_id") REFERENCES "public"."chatbot_message"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_provider" ADD CONSTRAINT "chatbot_provider_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_session" ADD CONSTRAINT "chatbot_session_chatbot_user_id_chatbot_users_id_fk" FOREIGN KEY ("chatbot_user_id") REFERENCES "public"."chatbot_users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_session" ADD CONSTRAINT "chatbot_session_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_users" ADD CONSTRAINT "chatbot_users_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_users" ADD CONSTRAINT "chatbot_users_chatbot_instance_id_chatbot_instance_id_fk" FOREIGN KEY ("chatbot_instance_id") REFERENCES "public"."chatbot_instance"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot" ADD CONSTRAINT "chatbot_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "dispatch_assignment" ADD CONSTRAINT "dispatch_assignment_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "dispatch_assignment" ADD CONSTRAINT "dispatch_assignment_operator_id_operators_id_fk" FOREIGN KEY ("operator_id") REFERENCES "public"."operators"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "dispatch_assignment" ADD CONSTRAINT "dispatch_assignment_ride_id_rides_id_fk" FOREIGN KEY ("ride_id") REFERENCES "public"."rides"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "driver_vehicle" ADD CONSTRAINT "driver_vehicle_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "driver_vehicle" ADD CONSTRAINT "driver_vehicle_driver_id_users_id_fk" FOREIGN KEY ("driver_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "driver_vehicle" ADD CONSTRAINT "driver_vehicle_vehicle_id_vehicle_id_fk" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicle"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "geodata_entries" ADD CONSTRAINT "geodata_entries_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "geodata_entries" ADD CONSTRAINT "geodata_entries_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "geodata_entries" ADD CONSTRAINT "geodata_entries_area_id_areas_id_fk" FOREIGN KEY ("area_id") REFERENCES "public"."areas"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "i18n_translations" ADD CONSTRAINT "i18n_translations_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "invoices" ADD CONSTRAINT "invoices_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "map_provider" ADD CONSTRAINT "map_provider_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "messages" ADD CONSTRAINT "messages_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "messages" ADD CONSTRAINT "messages_chat_id_chat_id_fk" FOREIGN KEY ("chat_id") REFERENCES "public"."chat"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "messages" ADD CONSTRAINT "messages_from_user_id_users_id_fk" FOREIGN KEY ("from_user_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "messages" ADD CONSTRAINT "messages_to_user_id_users_id_fk" FOREIGN KEY ("to_user_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "messages" ADD CONSTRAINT "messages_replied_to_message_id_messages_id_fk" FOREIGN KEY ("replied_to_message_id") REFERENCES "public"."messages"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "multi_tenant_group" ADD CONSTRAINT "multi_tenant_group_parent_group_id_multi_tenant_group_id_fk" FOREIGN KEY ("parent_group_id") REFERENCES "public"."multi_tenant_group"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "operator_extensions" ADD CONSTRAINT "operator_extensions_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "operator_extensions" ADD CONSTRAINT "operator_extensions_user_tenant_id_user_tenant_id_fk" FOREIGN KEY ("user_tenant_id") REFERENCES "public"."user_tenant"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "operator_shift" ADD CONSTRAINT "operator_shift_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "operator_shift" ADD CONSTRAINT "operator_shift_operator_id_operators_id_fk" FOREIGN KEY ("operator_id") REFERENCES "public"."operators"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "operators" ADD CONSTRAINT "operators_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "operators" ADD CONSTRAINT "operators_user_tenant_id_user_tenant_id_fk" FOREIGN KEY ("user_tenant_id") REFERENCES "public"."user_tenant"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "payment" ADD CONSTRAINT "payment_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "payment" ADD CONSTRAINT "payment_ride_id_rides_id_fk" FOREIGN KEY ("ride_id") REFERENCES "public"."rides"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "payment" ADD CONSTRAINT "payment_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pbx_call" ADD CONSTRAINT "pbx_call_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pbx_call" ADD CONSTRAINT "pbx_call_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pbx_call" ADD CONSTRAINT "pbx_call_operator_id_operators_id_fk" FOREIGN KEY ("operator_id") REFERENCES "public"."operators"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pbx_call" ADD CONSTRAINT "pbx_call_ride_id_rides_id_fk" FOREIGN KEY ("ride_id") REFERENCES "public"."rides"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "promos" ADD CONSTRAINT "promos_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "promotion" ADD CONSTRAINT "promotion_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "ride_event" ADD CONSTRAINT "ride_event_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "ride_event" ADD CONSTRAINT "ride_event_ride_id_ride_order_id_fk" FOREIGN KEY ("ride_id") REFERENCES "public"."ride_order"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "ride_rating" ADD CONSTRAINT "ride_rating_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "rides" ADD CONSTRAINT "rides_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "rides" ADD CONSTRAINT "rides_passenger_id_users_id_fk" FOREIGN KEY ("passenger_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "rides" ADD CONSTRAINT "rides_driver_id_users_id_fk" FOREIGN KEY ("driver_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "rides" ADD CONSTRAINT "rides_vehicle_id_vehicle_id_fk" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicle"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "roles" ADD CONSTRAINT "roles_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "system_settings" ADD CONSTRAINT "system_settings_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tenant_billing_profile" ADD CONSTRAINT "tenant_billing_profile_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tenant_bots" ADD CONSTRAINT "tenant_bots_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tenant_localization" ADD CONSTRAINT "tenant_localization_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tenant_settings" ADD CONSTRAINT "tenant_settings_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tenants" ADD CONSTRAINT "tenants_multi_tenant_group_id_multi_tenant_group_id_fk" FOREIGN KEY ("multi_tenant_group_id") REFERENCES "public"."multi_tenant_group"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_bot_roles" ADD CONSTRAINT "user_bot_roles_bot_id_bots_id_fk" FOREIGN KEY ("bot_id") REFERENCES "public"."bots"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_bot_roles" ADD CONSTRAINT "user_bot_roles_role_id_roles_id_fk" FOREIGN KEY ("role_id") REFERENCES "public"."roles"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_consent" ADD CONSTRAINT "user_consent_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_consent" ADD CONSTRAINT "user_consent_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_identity" ADD CONSTRAINT "user_identity_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_kyc" ADD CONSTRAINT "user_kyc_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_kyc" ADD CONSTRAINT "user_kyc_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_onboarding" ADD CONSTRAINT "user_onboarding_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_onboarding" ADD CONSTRAINT "user_onboarding_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_profile_history" ADD CONSTRAINT "user_profile_history_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_profile_history" ADD CONSTRAINT "user_profile_history_changed_by_id_users_id_fk" FOREIGN KEY ("changed_by_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_tenant" ADD CONSTRAINT "user_tenant_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_tenant" ADD CONSTRAINT "user_tenant_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_tenant" ADD CONSTRAINT "user_tenant_role_id_roles_id_fk" FOREIGN KEY ("role_id") REFERENCES "public"."roles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_tenant" ADD CONSTRAINT "user_tenant_invited_by_id_users_id_fk" FOREIGN KEY ("invited_by_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle" ADD CONSTRAINT "vehicle_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "verification_event" ADD CONSTRAINT "verification_event_chatbot_user_id_chatbot_users_id_fk" FOREIGN KEY ("chatbot_user_id") REFERENCES "public"."chatbot_users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "wallet" ADD CONSTRAINT "wallet_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "wallet" ADD CONSTRAINT "wallet_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "webhook_subscriber" ADD CONSTRAINT "webhook_subscriber_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "areas_tenant_id_idx" ON "areas" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "areas_deleted_at_idx" ON "areas" USING btree ("deleted_at") WHERE "areas"."deleted_at" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "audit_logs_tenant_id_idx" ON "audit_logs" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "audit_logs_actor_id_actor_type_idx" ON "audit_logs" USING btree ("actor_id","actor_type");--> statement-breakpoint
CREATE INDEX "audit_logs_event_type_idx" ON "audit_logs" USING btree ("event_type");--> statement-breakpoint
CREATE INDEX "audit_logs_target_table_target_id_idx" ON "audit_logs" USING btree ("target_table","target_id");--> statement-breakpoint
CREATE INDEX "audit_logs_created_at_idx" ON "audit_logs" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "audit_logs_deleted_at_idx" ON "audit_logs" USING btree ("deleted_at") WHERE "audit_logs"."deleted_at" IS NOT NULL;--> statement-breakpoint
CREATE UNIQUE INDEX "bots_webhook_path_segment_idx" ON "bots" USING btree ("webhook_path_segment");--> statement-breakpoint
CREATE INDEX "chatbot_events_chatbot_instance_id_idx" ON "chatbot_events" USING btree ("chatbot_instance_id");--> statement-breakpoint
CREATE INDEX "chatbot_events_user_id_idx" ON "chatbot_events" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "chatbot_events_session_id_idx" ON "chatbot_events" USING btree ("session_id");--> statement-breakpoint
CREATE INDEX "chatbot_events_message_id_idx" ON "chatbot_events" USING btree ("message_id");--> statement-breakpoint
CREATE INDEX "chatbot_events_event_type_idx" ON "chatbot_events" USING btree ("event_type");--> statement-breakpoint
CREATE INDEX "chatbot_events_occurred_at_idx" ON "chatbot_events" USING btree ("occurred_at");--> statement-breakpoint
CREATE INDEX "chatbot_events_deleted_at_idx" ON "chatbot_events" USING btree ("deleted_at") WHERE "chatbot_events"."deleted_at" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "chatbot_message_session_id_idx" ON "chatbot_message" USING btree ("chatbot_session_id");--> statement-breakpoint
CREATE INDEX "chatbot_message_user_id_idx" ON "chatbot_message" USING btree ("chatbot_user_id");--> statement-breakpoint
CREATE INDEX "chatbot_message_sent_at_idx" ON "chatbot_message" USING btree ("sent_at");--> statement-breakpoint
CREATE INDEX "chatbot_message_geolocation_idx" ON "chatbot_message_with_geolocation" USING gist ("location");--> statement-breakpoint
CREATE INDEX "chatbot_session_chatbot_user_id_idx" ON "chatbot_session" USING btree ("chatbot_user_id");--> statement-breakpoint
CREATE INDEX "chatbot_session_tenant_id_idx" ON "chatbot_session" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "chatbot_session_geolocation_idx" ON "chatbot_session" USING gist ("geolocation");--> statement-breakpoint
CREATE UNIQUE INDEX "provider_user_id_chatbot_instance_idx" ON "chatbot_users" USING btree ("provider_user_id","chatbot_instance_id") WHERE "chatbot_users"."provider_user_id" IS NOT NULL;--> statement-breakpoint
CREATE UNIQUE INDEX "user_id_chatbot_instance_idx" ON "chatbot_users" USING btree ("user_id","chatbot_instance_id");--> statement-breakpoint
CREATE INDEX "dispatch_assignment_tenant_id_idx" ON "dispatch_assignment" USING btree ("tenant_id");--> statement-breakpoint
CREATE UNIQUE INDEX "dispatch_assignment_operator_ride_idx" ON "dispatch_assignment" USING btree ("operator_id","ride_id");--> statement-breakpoint
CREATE INDEX "driver_vehicles_tenant_id_idx" ON "driver_vehicle" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "geodata_entries_tenant_id_idx" ON "geodata_entries" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "geodata_entries_user_id_idx" ON "geodata_entries" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "geodata_entries_area_id_idx" ON "geodata_entries" USING btree ("area_id");--> statement-breakpoint
CREATE INDEX "geodata_entries_source_idx" ON "geodata_entries" USING btree ("source");--> statement-breakpoint
CREATE INDEX "geodata_entries_geom_idx" ON "geodata_entries" USING gist ("geom");--> statement-breakpoint
CREATE INDEX "geodata_entries_deleted_at_idx" ON "geodata_entries" USING btree ("deleted_at") WHERE "geodata_entries"."deleted_at" IS NOT NULL;--> statement-breakpoint
CREATE UNIQUE INDEX "i18n_translations_tenant_locale_namespace_key_idx" ON "i18n_translations" USING btree ("tenant_id","locale","namespace","key");--> statement-breakpoint
CREATE INDEX "i18n_translations_tenant_id_idx" ON "i18n_translations" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "i18n_translations_locale_idx" ON "i18n_translations" USING btree ("locale");--> statement-breakpoint
CREATE INDEX "i18n_translations_key_idx" ON "i18n_translations" USING btree ("key");--> statement-breakpoint
CREATE INDEX "i18n_translations_deleted_at_idx" ON "i18n_translations" USING btree ("deleted_at") WHERE "i18n_translations"."deleted_at" IS NOT NULL;--> statement-breakpoint
CREATE UNIQUE INDEX "invoices_tenant_id_due_date_idx" ON "invoices" USING btree ("tenant_id","due_date");--> statement-breakpoint
CREATE INDEX "messages_tenant_id_idx" ON "messages" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "messages_chat_id_idx" ON "messages" USING btree ("chat_id");--> statement-breakpoint
CREATE INDEX "messages_from_user_id_idx" ON "messages" USING btree ("from_user_id");--> statement-breakpoint
CREATE INDEX "messages_to_user_id_idx" ON "messages" USING btree ("to_user_id");--> statement-breakpoint
CREATE INDEX "messages_sent_at_idx" ON "messages" USING btree ("sent_at");--> statement-breakpoint
CREATE INDEX "messages_message_type_idx" ON "messages" USING btree ("message_type");--> statement-breakpoint
CREATE INDEX "messages_phone_verified_idx" ON "messages" USING btree ("phone_verified");--> statement-breakpoint
CREATE INDEX "messages_geolocation_idx" ON "messages" USING gist ("geolocation") WHERE "messages"."geolocation" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "messages_deleted_at_idx" ON "messages" USING btree ("deleted_at") WHERE "messages"."deleted_at" IS NOT NULL;--> statement-breakpoint
CREATE UNIQUE INDEX "name_unique_per_parent" ON "multi_tenant_group" USING btree ("parent_group_id","name");--> statement-breakpoint
CREATE INDEX "status_idx" ON "multi_tenant_group" USING btree ("status");--> statement-breakpoint
CREATE INDEX "operator_extensions_tenant_id_idx" ON "operator_extensions" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "operators_tenant_id_idx" ON "operators" USING btree ("tenant_id");--> statement-breakpoint
CREATE UNIQUE INDEX "payment_processor_ref_idx" ON "payment" USING btree ("processor_ref");--> statement-breakpoint
CREATE UNIQUE INDEX "payment_user_status_idx" ON "payment" USING btree ("user_id","status");--> statement-breakpoint
CREATE UNIQUE INDEX "promo_code_idx" ON "promos" USING btree ("code");--> statement-breakpoint
CREATE INDEX "rides_tenant_id_idx" ON "rides" USING btree ("tenant_id");--> statement-breakpoint
CREATE UNIQUE INDEX "roles_tenant_id_name_idx" ON "roles" USING btree ("tenant_id","name");--> statement-breakpoint
CREATE UNIQUE INDEX "system_settings_key_tenant_id_idx" ON "system_settings" USING btree ("key","tenant_id");--> statement-breakpoint
CREATE INDEX "system_settings_tenant_id_idx" ON "system_settings" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "system_settings_key_idx" ON "system_settings" USING btree ("key");--> statement-breakpoint
CREATE INDEX "system_settings_deleted_at_idx" ON "system_settings" USING btree ("deleted_at") WHERE "system_settings"."deleted_at" IS NOT NULL;--> statement-breakpoint
CREATE UNIQUE INDEX "tenant_billing_profile_tenant_id_idx" ON "tenant_billing_profile" USING btree ("tenant_id");--> statement-breakpoint
CREATE UNIQUE INDEX "tenant_billing_profile_tax_id_unique" ON "tenant_billing_profile" USING btree ("tax_id");--> statement-breakpoint
CREATE UNIQUE INDEX "tenant_billing_profile_default_per_tenant" ON "tenant_billing_profile" USING btree ("tenant_id","default_profile");--> statement-breakpoint
CREATE UNIQUE INDEX "telegram_bot_token_idx" ON "tenant_bots" USING btree ("telegram_bot_token");--> statement-breakpoint
CREATE UNIQUE INDEX "tenant_localization_tenant_id_index" ON "tenant_localization" USING btree ("tenant_id");--> statement-breakpoint
CREATE UNIQUE INDEX "tenant_settings_tenant_id_key_unique" ON "tenant_settings" USING btree ("tenant_id","key");--> statement-breakpoint
CREATE UNIQUE INDEX "tenant_settings_tenant_id_idx" ON "tenant_settings" USING btree ("tenant_id");--> statement-breakpoint
CREATE UNIQUE INDEX "tenant_settings_key_idx" ON "tenant_settings" USING btree ("key");--> statement-breakpoint
CREATE UNIQUE INDEX "user_consent_user_consent_type_idx" ON "user_consent" USING btree ("user_id","consent_type");--> statement-breakpoint
CREATE UNIQUE INDEX "user_consent_deleted_at_idx" ON "user_consent" USING btree ("deleted_at");