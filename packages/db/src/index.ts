import { type PostgresJsDatabase, drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { config } from "./config.js";
import * as schema from "./schema/index.js"; // Import all schemas

// Re-export all Drizzle utilities to ensure type compatibility
export * from "drizzle-orm";
// Create a postgres client instance
const queryClient = postgres(config.DATABASE_URL);

// Create a Drizzle instance with proper typing
export const db: PostgresJsDatabase<typeof schema> = drizzle(queryClient, {
	schema,
});

// Export the fully typed db instance for consumers
export type DbType = typeof db;

// Re-export all schemas and individual schema objects
export * from "./schema/index.js";

// Export Bot types explicitly for easy access
export type { Bot, NewBot } from "./schema/index.js";
export { bots } from "./schema/index.js";

// Re-export types
export * from "./types/database.js";

// Re-export utils
export * from "./utils/bot-token.js";
