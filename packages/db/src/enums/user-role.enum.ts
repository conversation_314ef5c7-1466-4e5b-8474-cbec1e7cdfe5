import { pgEnum } from "drizzle-orm/pg-core";

// For database schema
export const userRoleDbEnum = pgEnum("user_role_enum", [
	// Or more specific like "user_tenants_role_enum"
	"PASSENGER",
	"DRIVER",
	"OPERATOR",
	"MANAGER",
	"ADMIN",
	"SYSTEM_SUPPORT", // Renamed from SUPPORT to avoid ambiguity
]);

// Export the enum values type
export type UserRoleDb = (typeof userRoleDbEnum.enumValues)[number];

// For TypeScript code (optional, but often convenient)
export enum UserRole {
	Passenger = "PASSENGER",
	Driver = "DRIVER",
	Operator = "OPERATOR",
	Manager = "MANAGER",
	Admin = "ADMIN",
	SystemSupport = "SYSTEM_SUPPORT",
}
