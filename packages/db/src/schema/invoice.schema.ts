import { relations } from "drizzle-orm";
import {
	jsonb,
	numeric,
	pgTable,
	timestamp,
	uniqueIndex,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { invoiceStatusEnum } from "../enums/invoice-status.enum.js";
import { InvoiceStatus } from "../enums/invoice-status.enum.js";
import { tenants } from "./tenants.schema.js";

export const invoices = pgTable(
	"invoices",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		tenantId: uuid("tenant_id")
			.references(() => tenants.id, { onDelete: "cascade" })
			.notNull(),
		amount: numeric("amount", { precision: 10, scale: 2 }).notNull(),
		currency: varchar("currency", { length: 3 }).notNull(),
		status: invoiceStatusEnum("status")
			.default(InvoiceStatus.PENDING) 
			.notNull(),
		dueDate: timestamp("due_date").notNull(),
		issuedAt: timestamp("issued_at").defaultNow().notNull(),
		paidAt: timestamp("paid_at"),
		metadata: jsonb("metadata").$type<Record<string, any>>(),
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at")
			.defaultNow()
			.notNull()
			.$onUpdate(() => new Date()),
		deletedAt: timestamp("deleted_at"),
	},
	(table) => [
		uniqueIndex("invoices_tenant_id_due_date_idx").on(
			table.tenantId,
			table.dueDate,
		),
	],
);

export const invoicesRelations = relations(invoices, ({ one }) => ({
	tenant: one(tenants, {
		fields: [invoices.tenantId],
		references: [tenants.id],
		relationName: "invoiceTenant",
	}),
}));

export type Invoice = typeof invoices.$inferSelect;
export type NewInvoice = typeof invoices.$inferInsert;
