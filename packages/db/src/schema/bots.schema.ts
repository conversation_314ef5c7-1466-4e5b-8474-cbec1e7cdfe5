import { relations } from "drizzle-orm";
import {
	boolean,
	jsonb,
	pgTable,
	text,
	timestamp,
	uniqueIndex,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { tenants } from "./tenants.schema.js";
import { userBotRoles } from "./user-bot-roles.schema.js";

export const bots = pgTable(
	"bots",
	{
		id: uuid("id").defaultRandom().primaryKey(),
		tenantId: uuid("tenant_id")
			.references(() => tenants.id, { onDelete: "cascade" })
			.notNull(),
		token: text("token").notNull(), // Store encrypted
		webhookPathSegment: varchar("webhook_path_segment", { length: 100 })
			.unique()
			.notNull(),
		botUsername: varchar("bot_username", { length: 255 }),
		isEnabled: boolean("is_enabled").default(true).notNull(),
		config: jsonb("config"), // For bot-specific settings
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at").defaultNow().notNull(),
	},
	(table) => [
		uniqueIndex("bots_webhook_path_segment_idx").on(table.webhookPathSegment),
	],
);

export type Bot = typeof bots.$inferSelect;
export type NewBot = typeof bots.$inferInsert;

export const botsRelations = relations(bots, ({ one, many }) => {
	// Note: Ensure userBotRoles is correctly imported if this was for circular deps
	return {
		tenant: one(tenants, {
			fields: [bots.tenantId],
			references: [tenants.id],
		}),
		userBotRoles: many(userBotRoles),
	};
});
