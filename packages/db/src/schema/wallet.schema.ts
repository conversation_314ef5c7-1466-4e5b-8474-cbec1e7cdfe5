import { relations } from "drizzle-orm";
import {
	numeric,
	pgTable,
	timestamp,
	uniqueIndex,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { tenants } from "./tenants.schema.js";
import { users } from "./users.schema.js";

export const wallets = pgTable(
	"wallet",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		userId: uuid("user_id")
			.references(() => users.id)
			.notNull(),
		tenantId: uuid("tenant_id") // Added tenantId
			.references(() => tenants.id, { onDelete: "cascade" }) // Corrected reference to tenants.id
			.notNull(),
		currency: varchar("currency", { length: 3 }).notNull(),
		balance: numeric("balance", { precision: 10, scale: 2 }) // Changed default value to string
			.default("0.0")
			.notNull(),
		updatedAt: timestamp("updated_at").defaultNow().notNull(),
	},
	(table) => ({
		indexes: [
			uniqueIndex("wallet_user_tenant_currency_idx").on(
				table.userId,
				table.tenantId,
				table.currency,
			),
		],
	}),
);

export const walletsRelations = relations(wallets, ({ one }) => ({
	tenant: one(tenants, {
		// Added tenant relation
		fields: [wallets.tenantId],
		references: [tenants.id],
	}),
	user: one(users, {
		fields: [wallets.userId],
		references: [users.id],
	}),
}));
