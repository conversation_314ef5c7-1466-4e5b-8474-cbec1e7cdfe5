import { relations } from "drizzle-orm";
import { index } from "drizzle-orm/pg-core";
import {
	boolean,
	geometry,
	jsonb,
	pgTable,
	timestamp,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { chatbotUsers } from "./chatbot-users.schema.js";
import { tenants } from "./tenants.schema.js"; // Ensure this path is correct

export const chatbotSessions = pgTable(
	"chatbot_session",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		chatbotUserId: uuid("chatbot_user_id")
			.references(() => chatbotUsers.id)
			.notNull(),
		// Could be "set null" or a more specific action if chatbot user is deleted
		tenantId: uuid("tenant_id")
			.references(() => tenants.id, { onDelete: "cascade" })
			.notNull(),
		startedAt: timestamp("started_at").notNull(),
		endedAt: timestamp("ended_at"),
		verified: boolean("verified").notNull().default(false),
		geolocation: geometry("geolocation", {
			type: "point",
			mode: "xy", // Changed to "xy" to match expected type
			srid: 4326,
		}), // PostGIS Geometry for WGS84 coordinates
		context: varchar("context", { length: 100 }),
		metadata: jsonb("metadata"),
	},
	(table) => [
		index("chatbot_session_chatbot_user_id_idx").on(table.chatbotUserId),
		index("chatbot_session_tenant_id_idx").on(table.tenantId),
		index("chatbot_session_geolocation_idx").using("gist", table.geolocation),
	],
);

export const chatbotSessionsRelations = relations(
	chatbotSessions,
	({ one }) => ({
		chatbotUser: one(chatbotUsers, {
			fields: [chatbotSessions.chatbotUserId],
			references: [chatbotUsers.id],
		}),
		tenant: one(tenants, {
			fields: [chatbotSessions.tenantId],
			references: [tenants.id],
		}),
	}),
);
