import { relations, sql } from "drizzle-orm";
import {
	index,
	pgTable,
	text,
	timestamp,
	uniqueIndex,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { tenants } from "./tenants.schema.js"; // For tenant_id foreign key

export const i18nTranslations = pgTable(
	"i18n_translations", // Pluralized table name
	{
		id: uuid("id").primaryKey().defaultRandom(),
		tenantId: uuid("tenant_id").references(() => tenants.id, {
			onDelete: "cascade",
		}), // Nullable for global translations
		namespace: varchar("namespace", { length: 100 }), // e.g., 'common', 'user_module', 'bot'
		key: varchar("key", { length: 255 }).notNull(),
		value: text("value").notNull(),
		locale: varchar("locale", { length: 10 }).notNull(), // e.g., 'en', 'es-MX'
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at")
			.defaultNow()
			.notNull()
			.$onUpdate(() => new Date()),
		deletedAt: timestamp("deleted_at"), // For soft deletes
	},
	(table) => [
		// Unique translation for a given tenant (or global), locale, namespace, and key
		uniqueIndex("i18n_translations_tenant_locale_namespace_key_idx").on(
			table.tenantId,
			table.locale,
			table.namespace, // Namespace can be null
			table.key,
		),
		index("i18n_translations_tenant_id_idx").on(table.tenantId),
		index("i18n_translations_locale_idx").on(table.locale),
		index("i18n_translations_key_idx").on(table.key),
		index("i18n_translations_deleted_at_idx")
			.on(table.deletedAt)
			.where(sql`${table.deletedAt} IS NOT NULL`),
	],
);

export const i18nTranslationsRelations = relations(
	i18nTranslations,
	({ one }) => ({
		tenant: one(tenants, {
			fields: [i18nTranslations.tenantId],
			references: [tenants.id],
			relationName: "tenantTranslations",
		}),
	}),
);

/*
This table can store global translations (where tenant_id is NULL)
and tenant-specific overrides (where tenant_id is set).
*/

export type I18nTranslation = typeof i18nTranslations.$inferSelect;
export type NewI18nTranslation = typeof i18nTranslations.$inferInsert;
