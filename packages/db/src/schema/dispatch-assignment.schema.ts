import { relations } from "drizzle-orm";
import {
	index,
	pgTable,
	timestamp,
	uniqueIndex,
	uuid,
} from "drizzle-orm/pg-core";
// Ensure these files exist or update the paths
import { operators } from "./operators.schema.js";
import { rides } from "./rides.schema.js";
import { tenants } from "./tenants.schema.js";

export const dispatchAssignment = pgTable(
	"dispatch_assignment",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		tenantId: uuid("tenant_id")
			.references(() => tenants.id, { onDelete: "cascade" })
			.notNull(),
		operatorId: uuid("operator_id")
			.references(() => operators.id)
			.notNull(),
		rideId: uuid("ride_id")
			.references(() => rides.id)
			.notNull(),
		assignedAt: timestamp("assigned_at").defaultNow(),
	},
	(table) => [
		index("dispatch_assignment_tenant_id_idx").on(table.tenantId),
		uniqueIndex("dispatch_assignment_operator_ride_idx").on(
			// Added table name to index
			table.operatorId,
			table.rideId,
		),
	],
);

export const dispatchAssignmentRelations = relations(
	dispatchAssignment,
	({ one }) => ({
		tenant: one(tenants, {
			fields: [dispatchAssignment.tenantId],
			references: [tenants.id],
		}),
		operator: one(operators, {
			fields: [dispatchAssignment.operatorId],
			references: [operators.id],
		}),
		ride: one(rides, {
			fields: [dispatchAssignment.rideId],
			references: [rides.id],
		}),
	}),
);
