import { relations } from "drizzle-orm";
import { integer, pgTable, text, timestamp, uuid } from "drizzle-orm/pg-core";
import { rideOrder } from "./ride-order.schema.js";
import { tenants } from "./tenants.schema.js";
import { users } from "./users.schema.js";

export const rideRatings = pgTable("ride_rating", {
	id: uuid("id").primaryKey().defaultRandom(),
	tenantId: uuid("tenant_id")
		.references(() => tenants.id, { onDelete: "cascade" })
		.notNull(),
	rideId: uuid("ride_id").notNull(),
	fromUserId: uuid("from_user_id").notNull(),
	toUserId: uuid("to_user_id").notNull(),
	rating: integer("rating").notNull(),
	feedback: text("feedback"),
	createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const rideRatingsRelations = relations(rideRatings, ({ one }) => ({
	tenant: one(tenants, {
		fields: [rideRatings.tenantId],
		references: [tenants.id],
	}),
	ride: one(rideOrder, {
		fields: [rideRatings.rideId],
		references: [rideOrder.id],
	}),
	fromUser: one(users, {
		fields: [rideRatings.fromUserId],
		references: [users.id],
	}),
	toUser: one(users, {
		fields: [rideRatings.toUserId],
		references: [users.id],
	}),
}));

export type RideRating = typeof rideRatings.$inferSelect;
export type NewRideRating = typeof rideRatings.$inferInsert;
