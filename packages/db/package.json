{"name": "@repo/db", "private": true, "version": "0.1.0", "description": "Database schema and utilities using Drizzle ORM", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsc", "typecheck": "tsc --noEmit", "lint": "eslint . --ext .ts", "format": "biome format src --write", "test": "echo 'No tests yet'", "clean": "rm -rf dist .turbo tsconfig.tsbuildinfo", "_generate_and_apply_migrations_interactive": "bun run ./scripts/generate-migrations.ts", "_apply_migrations": "bun run ./scripts/migrate.ts", "_seed_database": "bun run ./scripts/seed.ts", "_drop_recreate_db": "bun run ./scripts/drop-recreate-db.ts", "kit:generate": "bun x drizzle-kit generate", "kit:push": "bun x drizzle-kit push", "kit:studio": "bun x drizzle-kit studio", "check:exports": "bun run ./scripts/check-exports.ts"}, "dependencies": {"@repo/typescript-config": "workspace:^", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.0", "postgis": "^1.0.5", "env-var": "^7.5.0", "postgres": "^3.4.7"}, "devDependencies": {"@repo/eslint-config": "workspace:^", "drizzle-kit": "^0.31.1", "typescript": "^5.8.3"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./schema": {"import": "./dist/schema/index.js", "require": "./dist/schema/index.js", "types": "./dist/schema/index.d.ts"}, "./package.json": "./package.json"}, "imports": {"#*": "./dist/*"}}