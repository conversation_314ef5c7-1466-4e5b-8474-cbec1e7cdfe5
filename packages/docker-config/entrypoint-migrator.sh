#!/bin/sh
set -e

echo "[Migra<PERSON>] Waiting for PostgreSQL to be ready..."
while ! nc -z "${DB_HOST:-postgres}" "${DB_PORT:-5432}"; do
  echo "[Migra<PERSON>] PostgreSQL is unavailable - sleeping"
  sleep 1
done
echo "[Migra<PERSON>] PostgreSQL is up."

echo "[Migra<PERSON>] Running database migrations from /app/packages/db/scripts/migrate.ts..."
# Ensure DATABASE_URL is correctly passed as an environment variable to this container
# The migrate.ts script uses process.env.DATABASE_URL
(cd /app/packages/db && bun run ./scripts/migrate.ts)

echo "[Migra<PERSON>] Migrations script finished."
# This script will exit. Docker Compose will see its exit code.