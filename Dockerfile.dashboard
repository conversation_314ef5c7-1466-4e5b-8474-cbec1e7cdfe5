FROM oven/bun:1 AS base
WORKDIR /app

# Copy package files for dependency resolution
COPY package.json bun.lock ./
COPY apps/dashboard/package.json ./apps/dashboard/
COPY packages/typescript-config/package.json ./packages/typescript-config/
COPY packages/eslint-config/package.json ./packages/eslint-config/

# Install dependencies
RUN bun install

# Copy all source code
COPY . .

# Build the dashboard app
WORKDIR /app/apps/dashboard
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
RUN bun run build

# Production stage
FROM oven/bun:1-alpine AS runner
WORKDIR /app

ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# Create non-root user
RUN addgroup -S nextjs && adduser -S nextjs -G nextjs

# Copy built application and dependencies
COPY --from=base /app/apps/dashboard/.next/standalone/. ./
COPY --from=base /app/apps/dashboard/.next/static ./apps/dashboard/.next/static
COPY --from=base /app/apps/dashboard/public ./apps/dashboard/public

# Set proper ownership
RUN chown -R nextjs:nextjs /app

USER nextjs

EXPOSE 3000

ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

CMD ["bun", "apps/dashboard/server.js"]
