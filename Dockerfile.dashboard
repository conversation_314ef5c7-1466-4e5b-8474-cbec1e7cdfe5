FROM oven/bun:1 AS base
WORKDIR /app

# Install turbo globally for better caching
RUN bun add -g turbo

# Copy workspace configuration files
COPY package.json bun.lock turbo.json tsconfig.json ./
COPY packages/typescript-config/package.json ./packages/typescript-config/
COPY packages/eslint-config/package.json ./packages/eslint-config/
COPY apps/dashboard/package.json ./apps/dashboard/

# Install dependencies using Bun's workspace support
RUN bun install --frozen-lockfile

# Copy source code
COPY packages/ ./packages/
COPY apps/dashboard/ ./apps/dashboard/

# Build the dashboard app using Turborepo
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
RUN turbo build --filter=@repo/dashboard

# Production stage
FROM oven/bun:1-alpine AS runner
WORKDIR /app

ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# Create non-root user
RUN addgroup -S nextjs && adduser -S nextjs -G nextjs

# Copy built application and dependencies
COPY --from=base /app/apps/dashboard/.next/standalone/. ./
COPY --from=base /app/apps/dashboard/.next/static ./apps/dashboard/.next/static
COPY --from=base /app/apps/dashboard/public ./apps/dashboard/public

# Set proper ownership
RUN chown -R nextjs:nextjs /app

USER nextjs

# Environment configuration
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

CMD ["bun", "apps/dashboard/server.js"]
