# Database migrator for Taxi29 monorepo

# ---- Builder Stage (to get all dependencies and build @repo/db) ----
FROM oven/bun:1 AS base
WORKDIR /app

# Install turbo globally for better caching
RUN bun add -g turbo

# Copy workspace configuration
COPY package.json bun.lock turbo.json tsconfig.json ./
COPY packages/db/package.json ./packages/db/
COPY packages/typescript-config/package.json ./packages/typescript-config/
COPY packages/eslint-config/package.json ./packages/eslint-config/

# Install ALL dependencies for the db package and its scripts
RUN bun install

# Copy all source code for packages
COPY packages/ ./packages/

# Build the database package using Turborepo
RUN turbo build --filter=@repo/db


# ---- Final Migrator Stage ----
FROM oven/bun:1-alpine AS migrator
WORKDIR /app

# Install utilities needed by the entrypoint/wait script (if any)
RUN apk add --no-cache netcat-openbsd # For waiting for Postgres

# Copy only what's needed for migrations from the 'base' stage:
# - The db package (source, scripts, drizzle folder, package.json)
# - Its node_modules (or the relevant subset)

COPY --from=base /app/packages/db ./packages/db
COPY --from=base /app/node_modules ./node_modules

# Copy and set up entrypoint script (do this before changing user)
COPY ./packages/docker-config/entrypoint-migrator.sh /usr/local/bin/entrypoint-migrator.sh
RUN chmod +x /usr/local/bin/entrypoint-migrator.sh

# Create non-root user (good practice, though less critical for a short-lived job)
RUN addgroup -S appgroup && adduser -S appuser -G appgroup
RUN chown -R appuser:appgroup /app

USER appuser

ENTRYPOINT ["/usr/local/bin/entrypoint-migrator.sh"]
# Default command if not overridden (can be empty or a health check)
CMD ["echo", "Migration image ready. Override command in docker-compose to run migrations."]