# packages/docker-config/Dockerfile.migrator

# ---- Builder Stage (to get all dependencies and build @monorepo/db) ----
FROM oven/bun:1 AS base
WORKDIR /app

# Copy workspace configuration
COPY package.json bun.lock ./
COPY turbo.json ./
COPY tsconfig.json ./
COPY packages/db/package.json ./packages/db/ 
COPY packages/typescript-config/package.json ./packages/typescript-config/
COPY packages/eslint-config/package.json ./packages/eslint-config/

# Install ALL dependencies for the db package and its scripts
RUN bun install 

# Copy all source code (or at least packages/db)
COPY packages/db ./packages/db
# If scripts in packages/db import from other local packages, copy them too
# COPY packages/some-shared-util ./packages/some-shared-util

# Build the db package (if migrate.ts or its imports rely on compiled output from packages/db/src)
# Usually, scripts can run directly from TS with Bun, but if there are complex imports
# from within packages/db/src that need compilation, build it.
# For safety, let's assume it might be needed.
RUN bunx turbo run build --filter=@monorepo/db


# ---- Final Migrator Stage ----
FROM oven/bun:1-alpine AS migrator
WORKDIR /app

# Install utilities needed by the entrypoint/wait script (if any)
RUN apk add --no-cache netcat-openbsd # For waiting for Postgres

# Copy only what's needed for migrations from the 'base' stage:
# - The db package (source, scripts, drizzle folder, package.json)
# - Its node_modules (or the relevant subset)

COPY --from=base /app/packages/db ./packages/db
COPY --from=base /app/node_modules ./node_modules 
# Or, more selectively, copy only node_modules needed by packages/db/scripts/migrate.ts
# This would require knowing its exact dependency tree. Copying all is simpler for now.

# Ensure the migrate script is executable (though `bun run` handles this)
# RUN chmod +x ./packages/db/scripts/migrate.ts

# Create non-root user (good practice, though less critical for a short-lived job)
RUN addgroup -S appgroup && adduser -S appuser -G appgroup
RUN chown -R appuser:appgroup /app
USER appuser

# The CMD will be overridden by docker-compose, but an entrypoint can wait for DB
COPY ./packages/docker-config/entrypoint-migrator.sh /usr/local/bin/entrypoint-migrator.sh
RUN chmod +x /usr/local/bin/entrypoint-migrator.sh

ENTRYPOINT ["/usr/local/bin/entrypoint-migrator.sh"]
# Default command if not overridden (can be empty or a health check)
CMD ["echo", "Migration image ready. Override command in docker-compose to run migrations."]