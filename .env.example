# API Keys (Required to enable respective provider)
ANTHROPIC_API_KEY="your_anthropic_api_key_here"       # Required: Format: sk-ant-api03-...
PERPLEXITY_API_KEY="your_perplexity_api_key_here"     # Optional: Format: pplx-...
OPENAI_API_KEY="your_openai_api_key_here"             # Optional, for OpenAI/OpenRouter models. Format: sk-proj-...
GOOGLE_API_KEY="your_google_api_key_here"             # Optional, for Google Gemini models.
MISTRAL_API_KEY="your_mistral_key_here"               # Optional, for Mistral AI models.
XAI_API_KEY="YOUR_XAI_KEY_HERE"                       # Optional, for xAI AI models.
AZURE_OPENAI_API_KEY="your_azure_key_here"            # Optional, for Azure OpenAI models (requires endpoint in .taskmasterconfig).
OLLAMA_API_KEY="your_ollama_api_key_here"             # Optional: For remote Ollama servers that require authentication.

# Docker Production Environment Variables
# Database Configuration
DATABASE_URL="********************************************************/taxi29"
POSTGRES_USER="taxi29"
POSTGRES_PASSWORD="your_postgres_password"
POSTGRES_DB="taxi29"

# Redis Configuration
REDIS_PASSWORD="your_redis_password"

# Bot Configuration
PRIMARY_BOT_TOKEN="your_telegram_bot_token"
TOKEN_ENCRYPTION_KEY="your_32_character_encryption_key"

# RabbitMQ Configuration
RABBITMQ_USER="taxi29"
RABBITMQ_PASSWORD="your_rabbitmq_password"
RABBITMQ_PORT="5672"

# API URLs for Dashboard
DEV_API_URL="http://localhost:3004"
PROD_API_URL="http://main-api:3004"

# Application URLs
APP_URL="http://localhost:3000"

# Webhook Configuration (for production)
WEBHOOK_URL="https://your-domain.com/webhook"