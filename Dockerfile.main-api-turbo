# Multi-stage build for main-api
FROM oven/bun:1 AS base
WORKDIR /app

# Copy workspace configuration
COPY package.json bun.lock ./
COPY apps/main-api/package.json ./apps/main-api/
COPY packages/db/package.json ./packages/db/
COPY packages/typescript-config/package.json ./packages/typescript-config/
COPY packages/eslint-config/package.json ./packages/eslint-config/

# Install all dependencies with workspace support
RUN bun install

# Copy source code
COPY . .

# Ensure workspace packages are properly linked
# Check if workspace linking already worked, if not copy manually
RUN if [ ! -d "node_modules/@monorepo/db/dist" ]; then \
        echo "Workspace linking not working, copying manually..."; \
        mkdir -p node_modules/@monorepo/db && \
        cp -r packages/db/dist node_modules/@monorepo/db/ && \
        cp packages/db/package.json node_modules/@monorepo/db/; \
    else \
        echo "Workspace linking is working correctly"; \
    fi

# Verify workspace packages are available
RUN ls -la node_modules/@monorepo/db/

# Build the main-api application
RUN cd apps/main-api && bun run build

# Production stage
FROM oven/bun:1-alpine AS production
WORKDIR /app

# Copy built application
COPY --from=base /app/apps/main-api/dist ./dist
COPY --from=base /app/apps/main-api/package.json ./package.json

# Copy all dependencies from the base stage (includes @monorepo/db)
COPY --from=base /app/node_modules ./node_modules

# Fix broken symlinks by copying the actual built database package
RUN rm -rf ./node_modules/@monorepo/db && \
    mkdir -p ./node_modules/@monorepo/db
COPY --from=base /app/packages/db/dist ./node_modules/@monorepo/db/dist
COPY --from=base /app/packages/db/package.json ./node_modules/@monorepo/db/package.json

# Copy workspace configuration for reference
COPY --from=base /app/package.json ./workspace-package.json
COPY --from=base /app/bun.lock ./bun.lock

# Create non-root user
RUN addgroup -S appgroup && adduser -S appuser -G appgroup

# Set proper ownership
RUN chown -R appuser:appgroup /app

USER appuser

ENV NODE_ENV=production
EXPOSE 3004

CMD ["node", "dist/main.js"]



