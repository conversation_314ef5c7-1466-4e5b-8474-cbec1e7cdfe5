# Multi-stage build for main-api using Bun and Turborepo
FROM oven/bun:1 AS base
WORKDIR /app

# Install turbo globally for better caching
RUN bun add -g turbo

# Copy workspace configuration files
COPY package.json bun.lock turbo.json tsconfig.json ./
COPY packages/typescript-config/package.json ./packages/typescript-config/
COPY packages/eslint-config/package.json ./packages/eslint-config/
COPY packages/db/package.json ./packages/db/
COPY apps/main-api/package.json ./apps/main-api/

# Install dependencies using Bun's workspace support
RUN bun install

# Copy source code for dependencies
COPY packages/ ./packages/
COPY apps/main-api/ ./apps/main-api/

# Build dependencies first using Turborepo
RUN turbo build --filter=@repo/db

# Build the main-api application
RUN turbo build --filter=@repo/main-api

# Production stage
FROM oven/bun:1-alpine AS production
WORKDIR /app

# Copy built application and its dependencies
COPY --from=base /app/apps/main-api/dist ./dist
COPY --from=base /app/apps/main-api/package.json ./package.json

# Copy only production node_modules (Bun handles workspace linking properly)
COPY --from=base /app/node_modules ./node_modules

# Copy built database package
COPY --from=base /app/packages/db/dist ./node_modules/@repo/db/dist
COPY --from=base /app/packages/db/package.json ./node_modules/@repo/db/package.json

# Create non-root user for security
RUN addgroup -S appgroup && adduser -S appuser -G appgroup

# Set proper ownership
RUN chown -R appuser:appgroup /app

USER appuser

# Environment configuration
ENV NODE_ENV=production
ENV PORT=3004
EXPOSE 3004

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:3004/health || exit 1

CMD ["node", "dist/main.js"]



